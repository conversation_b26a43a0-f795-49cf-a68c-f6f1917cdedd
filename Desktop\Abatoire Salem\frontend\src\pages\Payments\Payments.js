import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Tabs,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
  List,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CreditCardOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  FilePdfOutlined,
  UserOutlined,
  BankOutlined,
  WalletOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { paymentsAPI, customersAPI, ordersAPI } from '../../services/api';
import { useNotifications } from '../../contexts/NotificationContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const Payments = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPayment, setEditingPayment] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [paymentDetailsVisible, setPaymentDetailsVisible] = useState(false);
  const [customerOrdersVisible, setCustomerOrdersVisible] = useState(false);
  const [selectedCustomerForOrders, setSelectedCustomerForOrders] = useState(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  // Fetch payments
  const { data: payments, isLoading } = useQuery(
    ['payments', { searchText, selectedCustomer, selectedMethod, dateRange }],
    () => paymentsAPI.getPayments({
      search: searchText,
      customer_id: selectedCustomer,
      payment_method: selectedMethod,
      start_date: dateRange?.[0]?.format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD'),
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch customers for filter
  const { data: customers } = useQuery('customers', customersAPI.getCustomers);

  // Fetch customer unpaid orders when creating payment
  const { data: customerOrders } = useQuery(
    ['customerUnpaidOrders', selectedCustomerForOrders],
    () => ordersAPI.getCustomerUnpaidOrders(selectedCustomerForOrders),
    {
      enabled: !!selectedCustomerForOrders,
    }
  );

  // Fetch statistics
  const { data: stats } = useQuery(
    ['paymentStats', dateRange],
    () => paymentsAPI.getStatistics({
      start_date: dateRange?.[0]?.format('YYYY-MM-DD') || dayjs().startOf('month').format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD') || dayjs().endOf('month').format('YYYY-MM-DD'),
    })
  );

  // Create payment mutation
  const createMutation = useMutation(paymentsAPI.createPayment, {
    onSuccess: () => {
      showSuccess('Paiement enregistré', 'Le paiement a été enregistré avec succès');
      setModalVisible(false);
      form.resetFields();
      setSelectedCustomerForOrders(null);
      queryClient.invalidateQueries('payments');
      queryClient.invalidateQueries('paymentStats');
      queryClient.invalidateQueries('customers');
    },
    onError: (error) => {
      showError('Erreur', error.response?.data?.message || 'Erreur lors de l\'enregistrement');
    },
  });

  // Update payment mutation
  const updateMutation = useMutation(
    ({ id, data }) => paymentsAPI.updatePayment(id, data),
    {
      onSuccess: () => {
        showSuccess('Paiement modifié', 'Le paiement a été modifié avec succès');
        setModalVisible(false);
        setEditingPayment(null);
        form.resetFields();
        queryClient.invalidateQueries('payments');
      },
      onError: (error) => {
        showError('Erreur', error.response?.data?.message || 'Erreur lors de la modification');
      },
    }
  );

  // Delete payment mutation
  const deleteMutation = useMutation(paymentsAPI.deletePayment, {
    onSuccess: () => {
      showSuccess('Paiement supprimé', 'Le paiement a été supprimé avec succès');
      queryClient.invalidateQueries('payments');
      queryClient.invalidateQueries('paymentStats');
      queryClient.invalidateQueries('customers');
    },
    onError: (error) => {
      showError('Erreur', error.response?.data?.message || 'Erreur lors de la suppression');
    },
  });

  const getPaymentMethodConfig = (method) => {
    const configs = {
      cash: { color: 'green', icon: <WalletOutlined />, text: 'Espèces' },
      credit: { color: 'blue', icon: <CreditCardOutlined />, text: 'Crédit' },
      check: { color: 'orange', icon: <BankOutlined />, text: 'Chèque' },
      transfer: { color: 'purple', icon: <BankOutlined />, text: 'Virement' },
    };
    return configs[method] || { color: 'default', icon: <DollarOutlined />, text: method };
  };

  const handleEdit = (payment) => {
    setEditingPayment(payment);
    form.setFieldsValue({
      customer_id: payment.customer_id,
      amount: payment.amount,
      payment_method: payment.payment_method,
      payment_date: dayjs(payment.payment_date),
      reference: payment.reference,
      notes: payment.notes,
    });
    setModalVisible(true);
  };

  const handleDelete = (paymentId) => {
    Modal.confirm({
      title: 'Confirmer la suppression',
      content: 'Êtes-vous sûr de vouloir supprimer ce paiement ? Cette action ne peut pas être annulée.',
      okText: 'Supprimer',
      cancelText: 'Annuler',
      okType: 'danger',
      onOk: () => deleteMutation.mutate(paymentId),
    });
  };

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment);
    setPaymentDetailsVisible(true);
  };

  const handleCustomerChange = (customerId) => {
    setSelectedCustomerForOrders(customerId);
    form.setFieldValue('customer_id', customerId);
  };

  const handleViewCustomerOrders = (customerId) => {
    setSelectedCustomerForOrders(customerId);
    setCustomerOrdersVisible(true);
  };

  const handleDownloadReceipt = async (paymentId) => {
    try {
      const response = await paymentsAPI.downloadReceipt(paymentId);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `recu_paiement_${paymentId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      message.error('Erreur lors du téléchargement du reçu');
    }
  };

  const handleSubmit = (values) => {
    const data = {
      ...values,
      payment_date: values.payment_date.format('YYYY-MM-DD'),
    };

    if (editingPayment) {
      updateMutation.mutate({ id: editingPayment.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'payment_date',
      key: 'payment_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
      sorter: true,
    },
    {
      title: 'Client',
      dataIndex: ['customer', 'name'],
      key: 'customer',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.customer?.phone}
          </Text>
        </div>
      ),
    },
    {
      title: 'Montant',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(2)} DA`,
      sorter: true,
    },
    {
      title: 'Mode de Paiement',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method) => {
        const config = getPaymentMethodConfig(method);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Référence',
      dataIndex: 'reference',
      key: 'reference',
      render: (text) => text || '-',
    },
    {
      title: 'Statut Allocation',
      key: 'allocation_status',
      render: (_, record) => {
        const allocated = record.allocated_amount || 0;
        const total = record.amount || 0;
        const percent = total > 0 ? Math.round((allocated / total) * 100) : 0;

        if (percent === 100) {
          return <Tag color="green" icon={<CheckCircleOutlined />}>Alloué</Tag>;
        } else if (percent > 0) {
          return <Tag color="orange">Partiel ({percent}%)</Tag>;
        } else {
          return <Tag color="red">Non alloué</Tag>;
        }
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Voir détails">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Télécharger reçu">
            <Button
              type="text"
              icon={<FilePdfOutlined />}
              onClick={() => handleDownloadReceipt(record.id)}
            />
          </Tooltip>
          <Tooltip title="Commandes client">
            <Button
              type="text"
              icon={<UserOutlined />}
              onClick={() => handleViewCustomerOrders(record.customer_id)}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              loading={deleteMutation.isLoading}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const statsData = stats?.data?.data || {};

  return (
    <div>
      <Title level={2}>
        <CreditCardOutlined /> Gestion des Paiements
      </Title>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Total Paiements"
              value={statsData.total_payments || 0}
              suffix="DA"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Paiements Aujourd'hui"
              value={statsData.today_payments || 0}
              suffix="DA"
              prefix={<CreditCardOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Créances Restantes"
              value={statsData.outstanding_amount || 0}
              suffix="DA"
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Nombre de Paiements"
              value={statsData.payments_count || 0}
              prefix={<CreditCardOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <Input
              placeholder="Rechercher par référence..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={5}>
            <Select
              placeholder="Filtrer par client"
              allowClear
              value={selectedCustomer}
              onChange={setSelectedCustomer}
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {customers?.data?.data?.data?.map((customer) => (
                <Option key={customer.id} value={customer.id}>
                  {customer.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Mode paiement"
              allowClear
              value={selectedMethod}
              onChange={setSelectedMethod}
              style={{ width: '100%' }}
            >
              <Option value="cash">Espèces</Option>
              <Option value="credit">Crédit</Option>
              <Option value="check">Chèque</Option>
              <Option value="transfer">Virement</Option>
            </Select>
          </Col>
          <Col xs={24} sm={5}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Date début', 'Date fin']}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
              block
            >
              Nouveau Paiement
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={payments?.data?.data?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: payments?.data?.data?.current_page,
            total: payments?.data?.data?.total,
            pageSize: payments?.data?.data?.per_page,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} paiements`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Payment Modal */}
      <Modal
        title={editingPayment ? 'Modifier le Paiement' : 'Nouveau Paiement'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPayment(null);
          setSelectedCustomerForOrders(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="customer_id"
                label="Client"
                rules={[{ required: true, message: 'Sélectionnez un client' }]}
              >
                <Select
                  placeholder="Sélectionnez un client"
                  showSearch
                  optionFilterProp="children"
                  onChange={handleCustomerChange}
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {customers?.data?.data?.data?.map((customer) => (
                    <Option key={customer.id} value={customer.id}>
                      <div>
                        <Text strong>{customer.name}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {customer.phone} - Crédit: {customer.total_unpaid_amount || 0} DA
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="amount"
                label="Montant (DA)"
                rules={[{ required: true, message: 'Saisissez le montant' }]}
              >
                <InputNumber
                  min={0.01}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Montant du paiement"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="payment_method"
                label="Mode de Paiement"
                rules={[{ required: true, message: 'Sélectionnez le mode de paiement' }]}
              >
                <Select placeholder="Mode de paiement">
                  <Option value="cash">
                    <WalletOutlined /> Espèces
                  </Option>
                  <Option value="credit">
                    <CreditCardOutlined /> Crédit
                  </Option>
                  <Option value="check">
                    <BankOutlined /> Chèque
                  </Option>
                  <Option value="transfer">
                    <BankOutlined /> Virement
                  </Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="payment_date"
                label="Date de Paiement"
                rules={[{ required: true, message: 'Sélectionnez la date' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="reference"
                label="Référence"
              >
                <Input placeholder="N° chèque, référence virement, etc." />
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="notes"
                label="Notes"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Notes additionnelles sur le paiement..."
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Customer Orders Preview */}
          {selectedCustomerForOrders && customerOrders?.data?.data?.length > 0 && (
            <div style={{ marginTop: '16px' }}>
              <Alert
                message="Commandes impayées du client"
                description={
                  <div>
                    <Text>Ce client a {customerOrders.data.data.length} commande(s) impayée(s).</Text>
                    <br />
                    <Text strong>
                      Total dû: {customerOrders.data.data.reduce((sum, order) => sum + (order.total_amount - order.paid_amount), 0).toFixed(2)} DA
                    </Text>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />

              <List
                size="small"
                dataSource={customerOrders.data.data.slice(0, 3)}
                renderItem={(order) => (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <Space>
                          <Tag color="blue">{order.order_number}</Tag>
                          <Text>{dayjs(order.order_date).format('DD/MM/YYYY')}</Text>
                        </Space>
                      }
                      description={
                        <Text>
                          Montant: {order.total_amount} DA -
                          Payé: {order.paid_amount} DA -
                          <Text strong style={{ color: '#ff4d4f' }}>
                            Reste: {(order.total_amount - order.paid_amount).toFixed(2)} DA
                          </Text>
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />

              {customerOrders.data.data.length > 3 && (
                <Text type="secondary">
                  ... et {customerOrders.data.data.length - 3} autre(s) commande(s)
                </Text>
              )}
            </div>
          )}

          <div style={{ textAlign: 'right', marginTop: '16px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Annuler
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isLoading || updateMutation.isLoading}
              >
                {editingPayment ? 'Modifier' : 'Enregistrer'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Payment Details Modal */}
      <PaymentDetailsModal
        payment={selectedPayment}
        visible={paymentDetailsVisible}
        onClose={() => {
          setPaymentDetailsVisible(false);
          setSelectedPayment(null);
        }}
      />

      {/* Customer Orders Modal */}
      <CustomerOrdersModal
        customerId={selectedCustomerForOrders}
        visible={customerOrdersVisible}
        onClose={() => {
          setCustomerOrdersVisible(false);
          setSelectedCustomerForOrders(null);
        }}
      />
    </div>
  );
};

// Payment Details Modal Component
const PaymentDetailsModal = ({ payment, visible, onClose }) => {
  if (!payment) return null;

  const getPaymentMethodConfig = (method) => {
    const configs = {
      cash: { color: 'green', icon: <WalletOutlined />, text: 'Espèces' },
      credit: { color: 'blue', icon: <CreditCardOutlined />, text: 'Crédit' },
      check: { color: 'orange', icon: <BankOutlined />, text: 'Chèque' },
      transfer: { color: 'purple', icon: <BankOutlined />, text: 'Virement' },
    };
    return configs[method] || { color: 'default', icon: <DollarOutlined />, text: method };
  };

  const methodConfig = getPaymentMethodConfig(payment.payment_method);
  const allocated = payment.allocated_amount || 0;
  const total = payment.amount || 0;
  const allocationPercent = total > 0 ? Math.round((allocated / total) * 100) : 0;

  return (
    <Modal
      title={`Détails Paiement - ${payment.id}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={700}
    >
      <Tabs defaultActiveKey="info">
        <TabPane tab="Informations" key="info">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="Informations Paiement" size="small">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Date">
                    {dayjs(payment.payment_date).format('DD/MM/YYYY')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Montant">
                    <Text strong style={{ fontSize: '16px', color: '#52c41a' }}>
                      {parseFloat(payment.amount).toFixed(2)} DA
                    </Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="Mode de Paiement">
                    <Tag color={methodConfig.color} icon={methodConfig.icon}>
                      {methodConfig.text}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="Référence">
                    {payment.reference || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="Enregistré le">
                    {dayjs(payment.created_at).format('DD/MM/YYYY HH:mm')}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>

            <Col xs={24} md={12}>
              <Card title="Informations Client" size="small">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Nom">
                    {payment.customer?.name}
                  </Descriptions.Item>
                  <Descriptions.Item label="Téléphone">
                    {payment.customer?.phone}
                  </Descriptions.Item>
                  <Descriptions.Item label="Email">
                    {payment.customer?.email || '-'}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>

          <Card title="Allocation du Paiement" size="small" style={{ marginTop: '16px' }}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>Montant alloué:</Text>
                <Text strong>{allocated.toFixed(2)} DA</Text>
              </div>
              <Progress
                percent={allocationPercent}
                status={allocationPercent === 100 ? 'success' : allocationPercent > 0 ? 'active' : 'exception'}
                strokeColor={allocationPercent === 100 ? '#52c41a' : '#1890ff'}
              />
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
                <Text type="secondary">Reste à allouer:</Text>
                <Text strong style={{ color: allocationPercent < 100 ? '#faad14' : '#52c41a' }}>
                  {(total - allocated).toFixed(2)} DA
                </Text>
              </div>
            </div>
          </Card>

          {payment.notes && (
            <Card title="Notes" size="small" style={{ marginTop: '16px' }}>
              <Text>{payment.notes}</Text>
            </Card>
          )}
        </TabPane>

        <TabPane tab="Allocations" key="allocations">
          <Table
            dataSource={payment.payment_allocations || []}
            columns={[
              {
                title: 'N° Commande',
                dataIndex: ['order', 'order_number'],
                key: 'order_number',
                render: (text) => <Tag color="blue">{text}</Tag>,
              },
              {
                title: 'Date Commande',
                dataIndex: ['order', 'order_date'],
                key: 'order_date',
                render: (date) => dayjs(date).format('DD/MM/YYYY'),
              },
              {
                title: 'Montant Alloué',
                dataIndex: 'allocated_amount',
                key: 'allocated_amount',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(2)} DA`,
              },
              {
                title: 'Date Allocation',
                dataIndex: 'created_at',
                key: 'created_at',
                render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm'),
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
            locale={{ emptyText: 'Aucune allocation pour ce paiement' }}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

// Customer Orders Modal Component
const CustomerOrdersModal = ({ customerId, visible, onClose }) => {
  const { data: customerOrders, isLoading } = useQuery(
    ['customerAllOrders', customerId],
    () => ordersAPI.getCustomerOrders(customerId),
    {
      enabled: visible && !!customerId,
    }
  );

  const { data: customer } = useQuery(
    ['customer', customerId],
    () => customersAPI.getCustomer(customerId),
    {
      enabled: visible && !!customerId,
    }
  );

  if (!customerId) return null;

  const orders = customerOrders?.data?.data || [];
  const unpaidOrders = orders.filter(order => order.payment_status !== 'paid');
  const totalUnpaid = unpaidOrders.reduce((sum, order) => sum + (order.total_amount - order.paid_amount), 0);

  return (
    <Modal
      title={`Commandes Client - ${customer?.data?.data?.name || 'Chargement...'}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
    >
      <div style={{ marginBottom: '16px' }}>
        <Alert
          message={`${unpaidOrders.length} commande(s) impayée(s)`}
          description={`Total impayé: ${totalUnpaid.toFixed(2)} DA`}
          type={unpaidOrders.length > 0 ? 'warning' : 'success'}
          showIcon
        />
      </div>

      <Table
        dataSource={orders}
        loading={isLoading}
        columns={[
          {
            title: 'N° Commande',
            dataIndex: 'order_number',
            key: 'order_number',
            render: (text) => <Tag color="blue">{text}</Tag>,
          },
          {
            title: 'Date',
            dataIndex: 'order_date',
            key: 'order_date',
            render: (date) => dayjs(date).format('DD/MM/YYYY'),
          },
          {
            title: 'Montant Total',
            dataIndex: 'total_amount',
            key: 'total_amount',
            align: 'right',
            render: (value) => `${parseFloat(value).toFixed(2)} DA`,
          },
          {
            title: 'Montant Payé',
            dataIndex: 'paid_amount',
            key: 'paid_amount',
            align: 'right',
            render: (value) => `${parseFloat(value || 0).toFixed(2)} DA`,
          },
          {
            title: 'Reste à Payer',
            key: 'remaining',
            align: 'right',
            render: (_, record) => {
              const remaining = record.total_amount - (record.paid_amount || 0);
              return (
                <Text strong style={{ color: remaining > 0 ? '#ff4d4f' : '#52c41a' }}>
                  {remaining.toFixed(2)} DA
                </Text>
              );
            },
          },
          {
            title: 'Statut Paiement',
            dataIndex: 'payment_status',
            key: 'payment_status',
            render: (status) => {
              const configs = {
                unpaid: { color: 'red', text: 'Impayé' },
                partial: { color: 'orange', text: 'Partiel' },
                paid: { color: 'green', text: 'Payé' },
              };
              const config = configs[status] || { color: 'default', text: status };
              return <Tag color={config.color}>{config.text}</Tag>;
            },
          },
          {
            title: 'Statut Commande',
            dataIndex: 'status',
            key: 'status',
            render: (status) => {
              const configs = {
                pending: { color: 'orange', text: 'En attente' },
                confirmed: { color: 'blue', text: 'Confirmée' },
                delivered: { color: 'green', text: 'Livrée' },
                cancelled: { color: 'red', text: 'Annulée' },
              };
              const config = configs[status] || { color: 'default', text: status };
              return <Tag color={config.color}>{config.text}</Tag>;
            },
          },
        ]}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} sur ${total} commandes`,
        }}
        size="small"
      />
    </Modal>
  );
};

export default Payments;
