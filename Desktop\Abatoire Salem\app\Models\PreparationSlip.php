<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PreparationSlip extends Model
{
    use HasFactory;

    protected $fillable = [
        'slip_number',
        'order_id',
        'prepared_by',
        'status',
        'created_date',
        'started_at',
        'completed_at',
        'notes',
    ];

    protected $casts = [
        'created_date' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Generate slip number before creating
        static::creating(function ($slip) {
            if (empty($slip->slip_number)) {
                $slip->slip_number = static::generateSlipNumber();
            }
        });
    }

    /**
     * Generate unique slip number
     */
    public static function generateSlipNumber(): string
    {
        $date = Carbon::now()->format('Ymd');
        $count = static::whereDate('created_at', Carbon::today())->count() + 1;
        return "PREP{$date}" . str_pad($count, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Start preparation
     */
    public function startPreparation(int $userId): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'prepared_by' => $userId,
            'started_at' => now(),
        ]);

        // Update order status
        $this->order->update(['status' => 'preparing']);

        return true;
    }

    /**
     * Complete preparation
     */
    public function completePreparation(): bool
    {
        if ($this->status !== 'in_progress') {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        // Update order status
        $this->order->update(['status' => 'prepared']);

        return true;
    }

    /**
     * Get preparation duration in minutes
     */
    public function getPreparationDurationAttribute(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->started_at->diffInMinutes($this->completed_at);
    }

    /**
     * Check if preparation is overdue
     */
    public function isOverdue(): bool
    {
        if ($this->status === 'completed') {
            return false;
        }

        // Consider overdue if started more than 2 hours ago
        return $this->started_at && $this->started_at->diffInHours(now()) > 2;
    }

    /**
     * Relationships
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function preparedBy()
    {
        return $this->belongsTo(User::class, 'prepared_by');
    }

    public function deliveryNote()
    {
        return $this->hasOne(DeliveryNote::class);
    }

    /**
     * Scopes
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'in_progress')
                    ->where('started_at', '<', now()->subHours(2));
    }

    public function scopeByPreparer($query, $userId)
    {
        return $query->where('prepared_by', $userId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_date', Carbon::today());
    }
}
