#!/bin/bash

echo "========================================"
echo "   ABATTOIR SALEM FRONTEND SETUP"
echo "        React Application"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}ERROR: Node.js is not installed${NC}"
    echo "Please install Node.js 16+ from https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo -e "${RED}ERROR: Node.js version $NODE_VERSION is too old${NC}"
    echo "Please install Node.js 16 or higher"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}ERROR: npm is not installed${NC}"
    echo "Please install Node.js which includes npm"
    exit 1
fi

echo -e "${GREEN}[1/4] Installing React dependencies...${NC}"
npm install
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to install dependencies${NC}"
    exit 1
fi

echo -e "${GREEN}[2/4] Creating environment file...${NC}"
if [ ! -f .env ]; then
    cat > .env << EOF
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_APP_NAME=Abattoir Salem
REACT_APP_VERSION=1.0.0
EOF
    echo "Environment file created."
else
    echo "Environment file already exists."
fi

echo -e "${GREEN}[3/4] Building production version...${NC}"
npm run build
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to build application${NC}"
    exit 1
fi

echo -e "${GREEN}[4/4] Testing development server...${NC}"
echo "Starting development server for testing..."
echo "Press Ctrl+C to stop the server when ready"
echo

# Start development server in background
npm start &
SERVER_PID=$!

echo
echo "========================================"
echo -e "${GREEN}     FRONTEND SETUP COMPLETED!${NC}"
echo "========================================"
echo
echo "The React application is now ready!"
echo
echo -e "${GREEN}Development server: http://localhost:3000${NC}"
echo -e "${GREEN}Production build: Available in 'build' folder${NC}"
echo
echo "To start development server: npm start"
echo "To build for production: npm run build"
echo "To serve production build: npm run serve"
echo
echo "Make sure the Laravel backend is running on:"
echo -e "${GREEN}http://localhost:8000${NC}"
echo

# Wait for user input to stop server
read -p "Press Enter to stop the development server..."
kill $SERVER_PID 2>/dev/null
