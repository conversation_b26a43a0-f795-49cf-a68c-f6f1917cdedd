import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  message,
  Tabs,
  Timeline,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
  Upload,
  Image,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  FilePdfOutlined,
  CameraOutlined,
  UserOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  CarOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { deliveriesAPI, ordersAPI } from '../../services/api';
import { useNotifications } from '../../contexts/NotificationContext';
import { useAuth } from '../../contexts/AuthContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const Deliveries = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [selectedDeliveryPerson, setSelectedDeliveryPerson] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  const [deliveryDetailsVisible, setDeliveryDetailsVisible] = useState(false);
  const [signatureModalVisible, setSignatureModalVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { showSuccess, showError } = useNotifications();

  // Fetch deliveries
  const { data: deliveries, isLoading } = useQuery(
    ['deliveries', { searchText, selectedStatus, selectedDeliveryPerson, dateRange }],
    () => deliveriesAPI.getDeliveries({
      search: searchText,
      status: selectedStatus,
      delivery_person_id: selectedDeliveryPerson,
      start_date: dateRange?.[0]?.format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD'),
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch delivery persons (users with delivery role)
  const { data: deliveryPersons } = useQuery(
    'deliveryPersons',
    deliveriesAPI.getDeliveryPersons
  );

  // Fetch statistics
  const { data: stats } = useQuery(
    ['deliveryStats', dateRange],
    () => deliveriesAPI.getStatistics({
      start_date: dateRange?.[0]?.format('YYYY-MM-DD') || dayjs().startOf('month').format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD') || dayjs().endOf('month').format('YYYY-MM-DD'),
    })
  );

  // Start delivery mutation
  const startDeliveryMutation = useMutation(deliveriesAPI.startDelivery, {
    onSuccess: () => {
      showSuccess('Livraison démarrée', 'La livraison a été démarrée avec succès');
      queryClient.invalidateQueries('deliveries');
      queryClient.invalidateQueries('deliveryStats');
    },
    onError: (error) => {
      showError('Erreur', error.response?.data?.message || 'Erreur lors du démarrage');
    },
  });

  // Complete delivery mutation
  const completeDeliveryMutation = useMutation(
    ({ id, data }) => deliveriesAPI.completeDelivery(id, data),
    {
      onSuccess: () => {
        showSuccess('Livraison terminée', 'La livraison a été marquée comme terminée');
        setSignatureModalVisible(false);
        form.resetFields();
        queryClient.invalidateQueries('deliveries');
        queryClient.invalidateQueries('deliveryStats');
      },
      onError: (error) => {
        showError('Erreur', error.response?.data?.message || 'Erreur lors de la finalisation');
      },
    }
  );

  // Return delivery mutation
  const returnDeliveryMutation = useMutation(
    ({ id, reason }) => deliveriesAPI.returnDelivery(id, { reason }),
    {
      onSuccess: () => {
        showSuccess('Livraison retournée', 'La livraison a été marquée comme retournée');
        queryClient.invalidateQueries('deliveries');
        queryClient.invalidateQueries('deliveryStats');
      },
      onError: (error) => {
        showError('Erreur', error.response?.data?.message || 'Erreur lors du retour');
      },
    }
  );

  const getStatusConfig = (status) => {
    const configs = {
      pending: { color: 'orange', icon: <ClockCircleOutlined />, text: 'En attente' },
      in_transit: { color: 'blue', icon: <TruckOutlined />, text: 'En transit' },
      delivered: { color: 'green', icon: <CheckCircleOutlined />, text: 'Livrée' },
      returned: { color: 'red', icon: <CloseCircleOutlined />, text: 'Retournée' },
      cancelled: { color: 'default', icon: <CloseCircleOutlined />, text: 'Annulée' },
    };
    return configs[status] || { color: 'default', icon: null, text: status };
  };

  const canStartDelivery = (delivery) => {
    return delivery.status === 'pending' && (user.role === 'admin' || user.role === 'delivery');
  };

  const canCompleteDelivery = (delivery) => {
    return delivery.status === 'in_transit' && (user.role === 'admin' || user.role === 'delivery');
  };

  const canReturnDelivery = (delivery) => {
    return delivery.status === 'in_transit' && (user.role === 'admin' || user.role === 'delivery');
  };

  const handleStartDelivery = (deliveryId) => {
    Modal.confirm({
      title: 'Démarrer la livraison',
      content: 'Êtes-vous sûr de vouloir démarrer cette livraison ?',
      okText: 'Démarrer',
      cancelText: 'Annuler',
      onOk: () => startDeliveryMutation.mutate(deliveryId),
    });
  };

  const handleCompleteDelivery = (delivery) => {
    setSelectedDelivery(delivery);
    setSignatureModalVisible(true);
  };

  const handleReturnDelivery = (deliveryId) => {
    Modal.confirm({
      title: 'Retourner la livraison',
      content: (
        <div>
          <p>Pourquoi cette livraison est-elle retournée ?</p>
          <Input.TextArea
            placeholder="Raison du retour..."
            id="return-reason"
            rows={3}
          />
        </div>
      ),
      okText: 'Retourner',
      cancelText: 'Annuler',
      okType: 'danger',
      onOk: () => {
        const reason = document.getElementById('return-reason')?.value || 'Retour sans raison';
        returnDeliveryMutation.mutate({ id: deliveryId, reason });
      },
    });
  };

  const handleViewDetails = (delivery) => {
    setSelectedDelivery(delivery);
    setDeliveryDetailsVisible(true);
  };

  const handleDownloadPDF = async (deliveryId) => {
    try {
      const response = await deliveriesAPI.downloadPDF(deliveryId);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `livraison_${deliveryId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      message.error('Erreur lors du téléchargement du PDF');
    }
  };

  const handleSignatureSubmit = (values) => {
    const formData = new FormData();
    formData.append('customer_signature', values.customer_signature?.file);
    formData.append('delivery_notes', values.delivery_notes || '');
    formData.append('delivered_at', dayjs().format('YYYY-MM-DD HH:mm:ss'));

    completeDeliveryMutation.mutate({
      id: selectedDelivery.id,
      data: formData,
    });
  };

  const columns = [
    {
      title: 'N° Livraison',
      dataIndex: 'delivery_number',
      key: 'delivery_number',
      render: (text) => <Tag color="purple">{text}</Tag>,
    },
    {
      title: 'N° Commande',
      dataIndex: ['order', 'order_number'],
      key: 'order_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Client',
      dataIndex: ['order', 'customer', 'name'],
      key: 'customer',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <PhoneOutlined /> {record.order?.customer?.phone}
          </Text>
        </div>
      ),
    },
    {
      title: 'Adresse Livraison',
      dataIndex: 'delivery_address',
      key: 'delivery_address',
      render: (text) => (
        <Tooltip title={text}>
          <Text ellipsis style={{ maxWidth: '150px' }}>
            <EnvironmentOutlined /> {text}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: 'Livreur',
      dataIndex: ['delivery_person', 'name'],
      key: 'delivery_person',
      render: (text) => text ? (
        <Text>
          <UserOutlined /> {text}
        </Text>
      ) : '-',
    },
    {
      title: 'Date Prévue',
      dataIndex: 'scheduled_date',
      key: 'scheduled_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Statut',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const config = getStatusConfig(status);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Voir détails">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>

          {canStartDelivery(record) && (
            <Tooltip title="Démarrer livraison">
              <Button
                type="text"
                icon={<TruckOutlined />}
                onClick={() => handleStartDelivery(record.id)}
                loading={startDeliveryMutation.isLoading}
              />
            </Tooltip>
          )}

          {canCompleteDelivery(record) && (
            <Tooltip title="Terminer livraison">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleCompleteDelivery(record)}
                loading={completeDeliveryMutation.isLoading}
              />
            </Tooltip>
          )}

          {canReturnDelivery(record) && (
            <Tooltip title="Retourner">
              <Button
                type="text"
                danger
                icon={<CloseCircleOutlined />}
                onClick={() => handleReturnDelivery(record.id)}
                loading={returnDeliveryMutation.isLoading}
              />
            </Tooltip>
          )}

          <Tooltip title="Télécharger PDF">
            <Button
              type="text"
              icon={<FilePdfOutlined />}
              onClick={() => handleDownloadPDF(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const statsData = stats?.data?.data || {};

  return (
    <div>
      <Title level={2}>
        <TruckOutlined /> Gestion des Livraisons
      </Title>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Livraisons Totales"
              value={statsData.total_deliveries || 0}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="En Transit"
              value={statsData.in_transit_deliveries || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Livrées"
              value={statsData.delivered_deliveries || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Retournées"
              value={statsData.returned_deliveries || 0}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <Input
              placeholder="Rechercher par N° livraison..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={5}>
            <Select
              placeholder="Filtrer par statut"
              allowClear
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
            >
              <Option value="pending">En attente</Option>
              <Option value="in_transit">En transit</Option>
              <Option value="delivered">Livrée</Option>
              <Option value="returned">Retournée</Option>
              <Option value="cancelled">Annulée</Option>
            </Select>
          </Col>
          <Col xs={24} sm={5}>
            <Select
              placeholder="Filtrer par livreur"
              allowClear
              value={selectedDeliveryPerson}
              onChange={setSelectedDeliveryPerson}
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {deliveryPersons?.data?.data?.map((person) => (
                <Option key={person.id} value={person.id}>
                  {person.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Date début', 'Date fin']}
            />
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={deliveries?.data?.data?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: deliveries?.data?.data?.current_page,
            total: deliveries?.data?.data?.total,
            pageSize: deliveries?.data?.data?.per_page,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} livraisons`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Signature Modal */}
      <Modal
        title="Finaliser la Livraison"
        open={signatureModalVisible}
        onCancel={() => {
          setSignatureModalVisible(false);
          setSelectedDelivery(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        {selectedDelivery && (
          <div>
            <Alert
              message={`Livraison: ${selectedDelivery.delivery_number}`}
              description={`Client: ${selectedDelivery.order?.customer?.name}`}
              type="info"
              style={{ marginBottom: '16px' }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSignatureSubmit}
            >
              <Form.Item
                name="customer_signature"
                label="Signature du Client"
                rules={[{ required: true, message: 'La signature du client est requise' }]}
              >
                <Upload
                  accept="image/*"
                  maxCount={1}
                  beforeUpload={() => false}
                  listType="picture-card"
                >
                  <div>
                    <CameraOutlined />
                    <div style={{ marginTop: 8 }}>Signature</div>
                  </div>
                </Upload>
              </Form.Item>

              <Form.Item
                name="delivery_notes"
                label="Notes de Livraison"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Notes sur la livraison (optionnel)..."
                />
              </Form.Item>

              <div style={{ textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setSignatureModalVisible(false)}>
                    Annuler
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={completeDeliveryMutation.isLoading}
                  >
                    Finaliser la Livraison
                  </Button>
                </Space>
              </div>
            </Form>
          </div>
        )}
      </Modal>

      {/* Delivery Details Modal */}
      <DeliveryDetailsModal
        delivery={selectedDelivery}
        visible={deliveryDetailsVisible}
        onClose={() => {
          setDeliveryDetailsVisible(false);
          setSelectedDelivery(null);
        }}
      />
    </div>
  );
};

// Delivery Details Modal Component
const DeliveryDetailsModal = ({ delivery, visible, onClose }) => {
  if (!delivery) return null;

  const getStatusConfig = (status) => {
    const configs = {
      pending: { color: 'orange', icon: <ClockCircleOutlined />, text: 'En attente' },
      in_transit: { color: 'blue', icon: <TruckOutlined />, text: 'En transit' },
      delivered: { color: 'green', icon: <CheckCircleOutlined />, text: 'Livrée' },
      returned: { color: 'red', icon: <CloseCircleOutlined />, text: 'Retournée' },
      cancelled: { color: 'default', icon: <CloseCircleOutlined />, text: 'Annulée' },
    };
    return configs[status] || { color: 'default', icon: null, text: status };
  };

  const statusConfig = getStatusConfig(delivery.status);

  return (
    <Modal
      title={`Détails Livraison - ${delivery.delivery_number}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Tabs defaultActiveKey="info">
        <TabPane tab="Informations" key="info">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="Informations Livraison" size="small">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="N° Livraison">
                    <Tag color="purple">{delivery.delivery_number}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="N° Commande">
                    <Tag color="blue">{delivery.order?.order_number}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="Statut">
                    <Tag color={statusConfig.color} icon={statusConfig.icon}>
                      {statusConfig.text}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="Date Prévue">
                    {dayjs(delivery.scheduled_date).format('DD/MM/YYYY')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Livreur">
                    {delivery.delivery_person?.name || 'Non assigné'}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>

            <Col xs={24} md={12}>
              <Card title="Informations Client" size="small">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Nom">
                    {delivery.order?.customer?.name}
                  </Descriptions.Item>
                  <Descriptions.Item label="Téléphone">
                    <PhoneOutlined /> {delivery.order?.customer?.phone}
                  </Descriptions.Item>
                  <Descriptions.Item label="Adresse Livraison">
                    <EnvironmentOutlined /> {delivery.delivery_address}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>

          {delivery.delivery_notes && (
            <Card title="Notes de Livraison" size="small" style={{ marginTop: '16px' }}>
              <Text>{delivery.delivery_notes}</Text>
            </Card>
          )}

          {delivery.return_reason && (
            <Card title="Raison du Retour" size="small" style={{ marginTop: '16px' }}>
              <Alert
                message={delivery.return_reason}
                type="error"
                showIcon
              />
            </Card>
          )}
        </TabPane>

        <TabPane tab="Historique" key="timeline">
          <Timeline>
            <Timeline.Item
              color="blue"
              dot={<ClockCircleOutlined />}
            >
              <div>
                <Text strong>Livraison créée</Text>
                <br />
                <Text type="secondary">
                  {dayjs(delivery.created_at).format('DD/MM/YYYY HH:mm')}
                </Text>
              </div>
            </Timeline.Item>

            {delivery.started_at && (
              <Timeline.Item
                color="orange"
                dot={<TruckOutlined />}
              >
                <div>
                  <Text strong>Livraison démarrée</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(delivery.started_at).format('DD/MM/YYYY HH:mm')}
                  </Text>
                  {delivery.delivery_person && (
                    <>
                      <br />
                      <Text type="secondary">
                        Par: {delivery.delivery_person.name}
                      </Text>
                    </>
                  )}
                </div>
              </Timeline.Item>
            )}

            {delivery.delivered_at && (
              <Timeline.Item
                color="green"
                dot={<CheckCircleOutlined />}
              >
                <div>
                  <Text strong>Livraison terminée</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(delivery.delivered_at).format('DD/MM/YYYY HH:mm')}
                  </Text>
                </div>
              </Timeline.Item>
            )}

            {delivery.returned_at && (
              <Timeline.Item
                color="red"
                dot={<CloseCircleOutlined />}
              >
                <div>
                  <Text strong>Livraison retournée</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(delivery.returned_at).format('DD/MM/YYYY HH:mm')}
                  </Text>
                  {delivery.return_reason && (
                    <>
                      <br />
                      <Text type="secondary">
                        Raison: {delivery.return_reason}
                      </Text>
                    </>
                  )}
                </div>
              </Timeline.Item>
            )}
          </Timeline>
        </TabPane>

        <TabPane tab="Articles" key="items">
          <Table
            dataSource={delivery.order?.order_items || []}
            columns={[
              {
                title: 'Produit',
                dataIndex: ['product', 'name'],
                key: 'product',
              },
              {
                title: 'Quantité',
                dataIndex: 'quantity_kg',
                key: 'quantity_kg',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(3)} kg`,
              },
              {
                title: 'Prix Unitaire',
                dataIndex: 'unit_price',
                key: 'unit_price',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(2)} DA`,
              },
              {
                title: 'Total',
                key: 'total',
                align: 'right',
                render: (_, record) => {
                  const total = record.quantity_kg * record.unit_price;
                  return `${total.toFixed(2)} DA`;
                },
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>

        {delivery.customer_signature && (
          <TabPane tab="Signature" key="signature">
            <div style={{ textAlign: 'center' }}>
              <Image
                src={delivery.customer_signature}
                alt="Signature du client"
                style={{ maxWidth: '100%', maxHeight: '300px' }}
              />
              <div style={{ marginTop: '16px' }}>
                <Text type="secondary">
                  Signature capturée le {dayjs(delivery.delivered_at).format('DD/MM/YYYY HH:mm')}
                </Text>
              </div>
            </div>
          </TabPane>
        )}
      </Tabs>
    </Modal>
  );
};

export default Deliveries;
