import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Tabs,
  Progress,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  CreditCardOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  FilePdfOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { customersAPI, paymentsAPI } from '../../services/api';
import { useNotifications } from '../../contexts/NotificationContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const Customers = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [showAtRisk, setShowAtRisk] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerDetailsVisible, setCustomerDetailsVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  // Fetch customers
  const { data: customers, isLoading } = useQuery(
    ['customers', { searchText, selectedStatus, showAtRisk }],
    () => customersAPI.getCustomers({
      search: searchText,
      status: selectedStatus,
      at_risk: showAtRisk,
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch customers at risk
  const { data: customersAtRisk } = useQuery(
    'customersAtRisk',
    customersAPI.getCustomersAtRisk
  );

  // Create customer mutation
  const createMutation = useMutation(customersAPI.createCustomer, {
    onSuccess: () => {
      showSuccess('Client créé', 'Le client a été créé avec succès');
      setModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('customers');
    },
    onError: (error) => {
      showError('Erreur', error.response?.data?.message || 'Erreur lors de la création');
    },
  });

  // Update customer mutation
  const updateMutation = useMutation(
    ({ id, data }) => customersAPI.updateCustomer(id, data),
    {
      onSuccess: () => {
        showSuccess('Client modifié', 'Le client a été modifié avec succès');
        setModalVisible(false);
        setEditingCustomer(null);
        form.resetFields();
        queryClient.invalidateQueries('customers');
      },
      onError: (error) => {
        showError('Erreur', error.response?.data?.message || 'Erreur lors de la modification');
      },
    }
  );

  const getCreditStatusConfig = (customer) => {
    const unpaidOrders = customer.unpaid_orders_count || 0;
    const maxUnpaid = customer.max_unpaid_orders || 3;
    const unpaidAmount = customer.total_unpaid_amount || 0;
    const creditLimit = customer.credit_limit || 0;

    if (unpaidOrders >= maxUnpaid || unpaidAmount >= creditLimit) {
      return {
        status: 'error',
        color: '#ff4d4f',
        text: 'BLOQUÉ',
        icon: <ExclamationCircleOutlined />,
      };
    } else if (unpaidOrders >= maxUnpaid - 1 || unpaidAmount >= creditLimit * 0.8) {
      return {
        status: 'warning',
        color: '#faad14',
        text: 'ATTENTION',
        icon: <WarningOutlined />,
      };
    } else {
      return {
        status: 'success',
        color: '#52c41a',
        text: 'NORMAL',
        icon: <CheckCircleOutlined />,
      };
    }
  };

  const getCreditUsagePercent = (customer) => {
    const unpaidAmount = customer.total_unpaid_amount || 0;
    const creditLimit = customer.credit_limit || 0;
    return creditLimit > 0 ? Math.round((unpaidAmount / creditLimit) * 100) : 0;
  };

  const handleEdit = (customer) => {
    setEditingCustomer(customer);
    form.setFieldsValue({
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
      credit_limit: customer.credit_limit,
      max_unpaid_orders: customer.max_unpaid_orders,
      notes: customer.notes,
    });
    setModalVisible(true);
  };

  const handleViewDetails = async (customer) => {
    setSelectedCustomer(customer);
    setCustomerDetailsVisible(true);
  };

  const handleSubmit = (values) => {
    if (editingCustomer) {
      updateMutation.mutate({ id: editingCustomer.id, data: values });
    } else {
      createMutation.mutate(values);
    }
  };

  const columns = [
    {
      title: 'Client',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.phone}
          </Text>
        </div>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (text) => text || '-',
    },
    {
      title: 'Limite Crédit',
      dataIndex: 'credit_limit',
      key: 'credit_limit',
      align: 'right',
      render: (value) => `${parseFloat(value || 0).toFixed(2)} DA`,
    },
    {
      title: 'Crédit Utilisé',
      key: 'credit_used',
      align: 'right',
      render: (_, record) => {
        const percent = getCreditUsagePercent(record);
        const config = getCreditStatusConfig(record);
        return (
          <div>
            <Progress
              percent={percent}
              size="small"
              status={config.status}
              style={{ marginBottom: '4px' }}
            />
            <Text style={{ fontSize: '11px' }}>
              {record.total_unpaid_amount || 0} DA ({percent}%)
            </Text>
          </div>
        );
      },
    },
    {
      title: 'Commandes Impayées',
      key: 'unpaid_orders',
      align: 'center',
      render: (_, record) => {
        const unpaidCount = record.unpaid_orders_count || 0;
        const maxUnpaid = record.max_unpaid_orders || 3;
        const config = getCreditStatusConfig(record);

        return (
          <Tag color={config.color} icon={config.icon}>
            {unpaidCount}/{maxUnpaid}
          </Tag>
        );
      },
    },
    {
      title: 'Statut Crédit',
      key: 'credit_status',
      render: (_, record) => {
        const config = getCreditStatusConfig(record);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Dernière Commande',
      dataIndex: 'last_order_date',
      key: 'last_order_date',
      render: (date) => date ? dayjs(date).format('DD/MM/YYYY') : '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Voir détails">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Commandes">
            <Button
              type="text"
              icon={<ShoppingCartOutlined />}
              onClick={() => navigate(`/orders?customer=${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="Paiements">
            <Button
              type="text"
              icon={<CreditCardOutlined />}
              onClick={() => navigate(`/payments?customer=${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const customersData = customers?.data?.data?.data || [];
  const atRiskData = customersAtRisk?.data?.data || [];

  return (
    <div>
      <Title level={2}>
        <UserOutlined /> Gestion des Clients
      </Title>

      {/* At Risk Alert */}
      {atRiskData.length > 0 && (
        <Alert
          message={`${atRiskData.length} client(s) à risque`}
          description="Certains clients ont atteint leurs limites de crédit ou de commandes impayées"
          type="warning"
          showIcon
          icon={<WarningOutlined />}
          style={{ marginBottom: '16px' }}
          action={
            <Button
              size="small"
              type="primary"
              onClick={() => setShowAtRisk(true)}
            >
              Voir les clients à risque
            </Button>
          }
        />
      )}

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Total Clients"
              value={customersData.length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Clients Actifs"
              value={customersData.filter(c => c.last_order_date).length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Clients à Risque"
              value={atRiskData.length}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Crédit Total Accordé"
              value={customersData.reduce((sum, c) => sum + (c.credit_limit || 0), 0)}
              suffix="DA"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Input
              placeholder="Rechercher par nom, téléphone ou email..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Filtrer par statut"
              allowClear
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
            >
              <Option value="active">Clients actifs</Option>
              <Option value="inactive">Clients inactifs</Option>
              <Option value="blocked">Clients bloqués</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Filtrer par risque"
              allowClear
              value={showAtRisk}
              onChange={setShowAtRisk}
              style={{ width: '100%' }}
            >
              <Option value={false}>Tous les clients</Option>
              <Option value={true}>Clients à risque uniquement</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
              block
            >
              Nouveau Client
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={customersData}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: customers?.data?.data?.current_page,
            total: customers?.data?.data?.total,
            pageSize: customers?.data?.data?.per_page,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} clients`,
          }}
          rowClassName={(record) => {
            const config = getCreditStatusConfig(record);
            return config.status === 'error' ? 'customer-at-risk' : '';
          }}
        />
      </Card>

      {/* Customer Modal */}
      <Modal
        title={editingCustomer ? 'Modifier le Client' : 'Nouveau Client'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCustomer(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="name"
                label="Nom du Client"
                rules={[{ required: true, message: 'Saisissez le nom du client' }]}
              >
                <Input placeholder="Nom complet du client" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="phone"
                label="Téléphone"
                rules={[{ required: true, message: 'Saisissez le numéro de téléphone' }]}
              >
                <Input placeholder="+213 XXX XXX XXX" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[{ type: 'email', message: 'Format email invalide' }]}
              >
                <Input placeholder="<EMAIL>" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="credit_limit"
                label="Limite de Crédit (DA)"
                rules={[{ required: true, message: 'Saisissez la limite de crédit' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Limite de crédit en DA"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="max_unpaid_orders"
                label="Max Commandes Impayées"
                rules={[{ required: true, message: 'Saisissez le nombre maximum' }]}
                initialValue={3}
              >
                <InputNumber
                  min={1}
                  max={10}
                  style={{ width: '100%' }}
                  placeholder="Nombre maximum de commandes impayées"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="address"
                label="Adresse"
              >
                <Input placeholder="Adresse du client" />
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="notes"
                label="Notes"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Notes additionnelles sur le client..."
                />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: '16px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Annuler
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isLoading || updateMutation.isLoading}
              >
                {editingCustomer ? 'Modifier' : 'Créer'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Customer Details Modal */}
      <CustomerDetailsModal
        customer={selectedCustomer}
        visible={customerDetailsVisible}
        onClose={() => {
          setCustomerDetailsVisible(false);
          setSelectedCustomer(null);
        }}
      />

      <style jsx>{`
        .customer-at-risk {
          background-color: #fff2f0 !important;
        }
      `}</style>
    </div>
  );
};

// Customer Details Modal Component
const CustomerDetailsModal = ({ customer, visible, onClose }) => {
  const [activeTab, setActiveTab] = useState('info');

  // Fetch customer orders
  const { data: customerOrders } = useQuery(
    ['customerOrders', customer?.id],
    () => customersAPI.getCustomerOrders(customer.id, { limit: 10 }),
    {
      enabled: visible && customer?.id,
    }
  );

  // Fetch customer payments
  const { data: customerPayments } = useQuery(
    ['customerPayments', customer?.id],
    () => customersAPI.getCustomerPayments(customer.id, { limit: 10 }),
    {
      enabled: visible && customer?.id,
    }
  );

  if (!customer) return null;

  const getCreditStatusConfig = (customer) => {
    const unpaidOrders = customer.unpaid_orders_count || 0;
    const maxUnpaid = customer.max_unpaid_orders || 3;
    const unpaidAmount = customer.total_unpaid_amount || 0;
    const creditLimit = customer.credit_limit || 0;

    if (unpaidOrders >= maxUnpaid || unpaidAmount >= creditLimit) {
      return {
        status: 'error',
        color: '#ff4d4f',
        text: 'BLOQUÉ',
        icon: <ExclamationCircleOutlined />,
      };
    } else if (unpaidOrders >= maxUnpaid - 1 || unpaidAmount >= creditLimit * 0.8) {
      return {
        status: 'warning',
        color: '#faad14',
        text: 'ATTENTION',
        icon: <WarningOutlined />,
      };
    } else {
      return {
        status: 'success',
        color: '#52c41a',
        text: 'NORMAL',
        icon: <CheckCircleOutlined />,
      };
    }
  };

  const getCreditUsagePercent = (customer) => {
    const unpaidAmount = customer.total_unpaid_amount || 0;
    const creditLimit = customer.credit_limit || 0;
    return creditLimit > 0 ? Math.round((unpaidAmount / creditLimit) * 100) : 0;
  };

  const creditConfig = getCreditStatusConfig(customer);
  const creditPercent = getCreditUsagePercent(customer);

  return (
    <Modal
      title={`Détails Client - ${customer.name}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Informations" key="info">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="Informations Générales" size="small">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Nom">{customer.name}</Descriptions.Item>
                  <Descriptions.Item label="Téléphone">{customer.phone}</Descriptions.Item>
                  <Descriptions.Item label="Email">{customer.email || '-'}</Descriptions.Item>
                  <Descriptions.Item label="Adresse">{customer.address || '-'}</Descriptions.Item>
                  <Descriptions.Item label="Date création">
                    {dayjs(customer.created_at).format('DD/MM/YYYY')}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>

            <Col xs={24} md={12}>
              <Card title="Statut Crédit" size="small">
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <Text>Crédit utilisé:</Text>
                    <Text strong>{customer.total_unpaid_amount || 0} DA</Text>
                  </div>
                  <Progress
                    percent={creditPercent}
                    status={creditConfig.status}
                    strokeColor={creditConfig.color}
                  />
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
                    <Text type="secondary">Limite: {customer.credit_limit} DA</Text>
                    <Tag color={creditConfig.color} icon={creditConfig.icon}>
                      {creditConfig.text}
                    </Tag>
                  </div>
                </div>

                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Commandes impayées">
                    {customer.unpaid_orders_count || 0}/{customer.max_unpaid_orders || 3}
                  </Descriptions.Item>
                  <Descriptions.Item label="Dernière commande">
                    {customer.last_order_date ? dayjs(customer.last_order_date).format('DD/MM/YYYY') : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="Total acheté">
                    {customer.total_orders_amount || 0} DA
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>

          {customer.notes && (
            <Card title="Notes" size="small" style={{ marginTop: '16px' }}>
              <Text>{customer.notes}</Text>
            </Card>
          )}
        </TabPane>

        <TabPane tab="Commandes Récentes" key="orders">
          <Table
            dataSource={customerOrders?.data?.data || []}
            columns={[
              {
                title: 'N° Commande',
                dataIndex: 'order_number',
                key: 'order_number',
                render: (text) => <Tag color="blue">{text}</Tag>,
              },
              {
                title: 'Date',
                dataIndex: 'order_date',
                key: 'order_date',
                render: (date) => dayjs(date).format('DD/MM/YYYY'),
              },
              {
                title: 'Montant',
                dataIndex: 'total_amount',
                key: 'total_amount',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(2)} DA`,
              },
              {
                title: 'Statut',
                dataIndex: 'status',
                key: 'status',
                render: (status) => {
                  const configs = {
                    pending: { color: 'orange', text: 'En attente' },
                    confirmed: { color: 'blue', text: 'Confirmée' },
                    delivered: { color: 'green', text: 'Livrée' },
                    cancelled: { color: 'red', text: 'Annulée' },
                  };
                  const config = configs[status] || { color: 'default', text: status };
                  return <Tag color={config.color}>{config.text}</Tag>;
                },
              },
              {
                title: 'Paiement',
                dataIndex: 'payment_status',
                key: 'payment_status',
                render: (status) => {
                  const configs = {
                    unpaid: { color: 'red', text: 'Impayé' },
                    partial: { color: 'orange', text: 'Partiel' },
                    paid: { color: 'green', text: 'Payé' },
                  };
                  const config = configs[status] || { color: 'default', text: status };
                  return <Tag color={config.color}>{config.text}</Tag>;
                },
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>

        <TabPane tab="Paiements Récents" key="payments">
          <Table
            dataSource={customerPayments?.data?.data || []}
            columns={[
              {
                title: 'Date',
                dataIndex: 'payment_date',
                key: 'payment_date',
                render: (date) => dayjs(date).format('DD/MM/YYYY'),
              },
              {
                title: 'Montant',
                dataIndex: 'amount',
                key: 'amount',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(2)} DA`,
              },
              {
                title: 'Mode',
                dataIndex: 'payment_method',
                key: 'payment_method',
                render: (method) => {
                  const methods = {
                    cash: 'Espèces',
                    credit: 'Crédit',
                    check: 'Chèque',
                    transfer: 'Virement',
                  };
                  return methods[method] || method;
                },
              },
              {
                title: 'Référence',
                dataIndex: 'reference',
                key: 'reference',
                render: (text) => text || '-',
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default Customers;
