<?php

namespace App\Http\Controllers\Reception;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Reception;
use App\Models\Product;
use App\Models\ActivityLog;
use Carbon\Carbon;

class ReceptionController extends Controller
{
    /**
     * Display a listing of receptions
     */
    public function index(Request $request)
    {
        $query = Reception::with(['product', 'user', 'stock']);

        // Apply filters
        if ($request->has('product_id') && $request->product_id) {
            $query->byProduct($request->product_id);
        }

        if ($request->has('lot_number') && $request->lot_number) {
            $query->byLot($request->lot_number);
        }

        if ($request->has('origin') && $request->origin) {
            $query->byOrigin($request->origin);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'reception_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $receptions = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $receptions,
            'message' => 'Receptions retrieved successfully'
        ]);
    }

    /**
     * Store a newly created reception
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'quantity_heads' => 'required|integer|min:1',
            'total_weight_kg' => 'required|numeric|min:0.001',
            'origin' => 'nullable|string|max:255',
            'slaughter_date' => 'required|date|before_or_equal:today',
            'reception_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if product is active
        $product = Product::find($request->product_id);
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Product is not active'
            ], 400);
        }

        // Generate lot number if not provided
        $lotNumber = $request->lot_number ?? Reception::generateLotNumber();

        // Check if lot number already exists
        if (Reception::where('lot_number', $lotNumber)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Lot number already exists'
            ], 400);
        }

        try {
            $reception = Reception::create([
                'product_id' => $request->product_id,
                'user_id' => auth()->id(),
                'lot_number' => $lotNumber,
                'quantity_heads' => $request->quantity_heads,
                'total_weight_kg' => $request->total_weight_kg,
                'origin' => $request->origin,
                'slaughter_date' => $request->slaughter_date,
                'reception_date' => $request->reception_date,
                'notes' => $request->notes,
            ]);

            // Log activity
            ActivityLog::logActivity(
                'created',
                "Created reception {$reception->lot_number} for {$reception->product->name}",
                'Reception',
                $reception->id,
                null,
                $reception->toArray()
            );

            // Load relationships for response
            $reception->load(['product', 'user', 'stock']);

            return response()->json([
                'success' => true,
                'data' => $reception,
                'message' => 'Reception created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create reception: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified reception
     */
    public function show(Reception $reception)
    {
        $reception->load(['product', 'user', 'stock.movements.user']);

        return response()->json([
            'success' => true,
            'data' => $reception,
            'message' => 'Reception retrieved successfully'
        ]);
    }

    /**
     * Update the specified reception
     */
    public function update(Request $request, Reception $reception)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'sometimes|exists:products,id',
            'quantity_heads' => 'sometimes|integer|min:1',
            'total_weight_kg' => 'sometimes|numeric|min:0.001',
            'origin' => 'nullable|string|max:255',
            'slaughter_date' => 'sometimes|date|before_or_equal:today',
            'reception_date' => 'sometimes|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if reception has been used (has stock movements)
        if ($reception->stock && $reception->stock->movements()->count() > 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update reception that has stock movements'
            ], 400);
        }

        $oldValues = $reception->toArray();

        try {
            $reception->update($request->only([
                'product_id', 'quantity_heads', 'total_weight_kg', 
                'origin', 'slaughter_date', 'reception_date', 'notes'
            ]));

            // Update stock if weight changed
            if ($request->has('total_weight_kg') && $reception->stock) {
                $reception->stock->update([
                    'current_weight_kg' => $request->total_weight_kg,
                    'initial_weight_kg' => $request->total_weight_kg,
                ]);

                // Update the initial stock movement
                $initialMovement = $reception->stock->movements()->first();
                if ($initialMovement) {
                    $initialMovement->update([
                        'quantity_kg' => $request->total_weight_kg,
                        'balance_after' => $request->total_weight_kg,
                    ]);
                }
            }

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Updated reception {$reception->lot_number}",
                'Reception',
                $reception->id,
                $oldValues,
                $reception->fresh()->toArray()
            );

            $reception->load(['product', 'user', 'stock']);

            return response()->json([
                'success' => true,
                'data' => $reception,
                'message' => 'Reception updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update reception: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified reception
     */
    public function destroy(Reception $reception)
    {
        // Check if reception has been used (has stock movements beyond initial)
        if ($reception->stock && $reception->stock->movements()->count() > 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete reception that has stock movements'
            ], 400);
        }

        try {
            $lotNumber = $reception->lot_number;
            
            // Delete related stock and movements
            if ($reception->stock) {
                $reception->stock->movements()->delete();
                $reception->stock->delete();
            }

            $reception->delete();

            // Log activity
            ActivityLog::logActivity(
                'deleted',
                "Deleted reception {$lotNumber}",
                'Reception',
                $reception->id,
                $reception->toArray(),
                null
            );

            return response()->json([
                'success' => true,
                'message' => 'Reception deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete reception: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reception statistics
     */
    public function statistics(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $stats = [
            'total_receptions' => Reception::byDateRange($startDate, $endDate)->count(),
            'total_weight' => Reception::byDateRange($startDate, $endDate)->sum('total_weight_kg'),
            'total_heads' => Reception::byDateRange($startDate, $endDate)->sum('quantity_heads'),
            'by_product' => Reception::byDateRange($startDate, $endDate)
                ->with('product')
                ->get()
                ->groupBy('product.name')
                ->map(function ($receptions) {
                    return [
                        'count' => $receptions->count(),
                        'total_weight' => $receptions->sum('total_weight_kg'),
                        'total_heads' => $receptions->sum('quantity_heads'),
                    ];
                }),
            'by_origin' => Reception::byDateRange($startDate, $endDate)
                ->whereNotNull('origin')
                ->get()
                ->groupBy('origin')
                ->map(function ($receptions) {
                    return [
                        'count' => $receptions->count(),
                        'total_weight' => $receptions->sum('total_weight_kg'),
                    ];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Reception statistics retrieved successfully'
        ]);
    }

    /**
     * Generate next lot number
     */
    public function generateLotNumber()
    {
        return response()->json([
            'success' => true,
            'data' => ['lot_number' => Reception::generateLotNumber()],
            'message' => 'Lot number generated successfully'
        ]);
    }
}
