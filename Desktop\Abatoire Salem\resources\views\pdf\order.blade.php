@extends('pdf.layout')

@section('title', 'Bon de Commande')

@section('document-title')
    Bon de Commande N° {{ $order->order_number }}
@endsection

@section('content')
    <div class="document-info">
        <div class="left">
            <div class="info-box">
                <div class="info-title">Informations Client</div>
                <div class="info-content">
                    <strong>{{ $order->customer->name }}</strong><br>
                    @if($order->customer->address)
                        {{ $order->customer->address }}<br>
                    @endif
                    @if($order->customer->phone)
                        Tél: {{ $order->customer->phone }}<br>
                    @endif
                    @if($order->customer->email)
                        Email: {{ $order->customer->email }}
                    @endif
                </div>
            </div>
        </div>
        
        <div class="right">
            <div class="info-box">
                <div class="info-title">Détails de la Commande</div>
                <div class="info-content">
                    <strong>N° Commande:</strong> {{ $order->order_number }}<br>
                    <strong>Date:</strong> {{ $order->order_date->format('d/m/Y') }}<br>
                    <strong>Statut:</strong> 
                    <span class="status-badge status-{{ $order->status }}">{{ ucfirst($order->status) }}</span><br>
                    <strong>Mode de paiement:</strong> {{ ucfirst($order->payment_method) }}<br>
                    @if($order->delivery_date)
                        <strong>Date de livraison:</strong> {{ $order->delivery_date->format('d/m/Y') }}<br>
                    @endif
                    <strong>Vendeur:</strong> {{ $order->user->name }}
                </div>
            </div>
        </div>
    </div>

    @if($order->notes)
        <div class="highlight">
            <strong>Notes:</strong> {{ $order->notes }}
        </div>
    @endif

    <table class="table">
        <thead>
            <tr>
                <th style="width: 5%">#</th>
                <th style="width: 35%">Produit</th>
                <th style="width: 15%">Type Animal</th>
                <th style="width: 15%" class="center">Quantité (kg)</th>
                <th style="width: 15%" class="center">Prix Unitaire (DA)</th>
                <th style="width: 15%" class="center">Total (DA)</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->orderItems as $index => $item)
                <tr>
                    <td class="center">{{ $index + 1 }}</td>
                    <td>{{ $item->product->name }}</td>
                    <td>{{ $item->product->animal_type }}</td>
                    <td class="number">{{ number_format($item->quantity_kg, 3) }}</td>
                    <td class="number">{{ number_format($item->unit_price, 2) }}</td>
                    <td class="number">{{ number_format($item->total_price, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="totals">
        <div class="total-row">
            <strong>Sous-total: {{ number_format($order->total_amount, 2) }} DA</strong>
        </div>
        <div class="total-row">
            <strong>TVA (0%): 0.00 DA</strong>
        </div>
        <div class="total-row grand-total">
            <strong>Total TTC: {{ number_format($order->total_amount, 2) }} DA</strong>
        </div>
    </div>

    <div class="document-info mt-20">
        <div class="left">
            <div class="info-box">
                <div class="info-title">Statut de Paiement</div>
                <div class="info-content">
                    <strong>Statut:</strong> 
                    <span class="status-badge status-{{ $order->payment_status }}">
                        {{ ucfirst($order->payment_status) }}
                    </span><br>
                    <strong>Montant payé:</strong> {{ number_format($order->paid_amount, 2) }} DA<br>
                    <strong>Montant restant:</strong> {{ number_format($order->total_amount - $order->paid_amount, 2) }} DA
                </div>
            </div>
        </div>
        
        <div class="right">
            <div class="info-box">
                <div class="info-title">Informations Crédit Client</div>
                <div class="info-content">
                    <strong>Limite de crédit:</strong> {{ number_format($order->customer->credit_limit, 2) }} DA<br>
                    <strong>Commandes impayées:</strong> {{ $order->customer->unpaid_orders_count }}/{{ $order->customer->max_unpaid_orders }}<br>
                    <strong>Montant impayé total:</strong> {{ number_format($order->customer->total_unpaid_amount, 2) }} DA
                </div>
            </div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-title">Client</div>
            <div class="signature-line">Signature et cachet</div>
        </div>
        
        <div class="signature-box">
            <div class="signature-title">Vendeur</div>
            <div class="signature-line">{{ $order->user->name }}</div>
        </div>
        
        <div class="signature-box">
            <div class="signature-title">Direction</div>
            <div class="signature-line">Signature et cachet</div>
        </div>
    </div>

    @if($order->status === 'pending')
        <div class="highlight mt-20">
            <strong>Important:</strong> Cette commande est en attente de confirmation. 
            Les prix et disponibilités sont susceptibles de changer jusqu'à confirmation.
        </div>
    @endif

    <div class="mt-20 font-small text-center">
        <strong>Conditions générales:</strong><br>
        - Les prix sont exprimés en Dinars Algériens (DA) TTC<br>
        - La marchandise voyage aux risques et périls du destinataire<br>
        - Tout retour de marchandise doit être signalé dans les 24h<br>
        - Paiement comptant ou selon accord de crédit établi
    </div>
@endsection
