<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stock_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who made the movement
            $table->enum('movement_type', ['in', 'out', 'adjustment', 'reservation', 'unreservation']);
            $table->decimal('quantity_kg', 10, 3); // Positive for in, negative for out
            $table->decimal('balance_after', 10, 3); // Stock balance after this movement
            $table->string('reference_type')->nullable(); // 'order', 'reception', 'adjustment'
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the related record
            $table->text('reason')->nullable(); // Reason for movement
            $table->datetime('movement_date');
            $table->timestamps();
            
            // Index for better performance on traceability queries
            $table->index(['stock_id', 'movement_date']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
