@echo off
echo ========================================
echo    ABATTOIR SALEM SETUP VERIFICATION
echo ========================================
echo.

echo Checking PHP...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP not found or not working
    echo Please install PHP 8.1+ and add to PATH
    goto :error
) else (
    echo ✅ PHP is working!
)
echo.

echo Checking Composer...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer not found
    echo Please install Composer from https://getcomposer.org/
    goto :error
) else (
    echo ✅ Composer is working!
)
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    goto :error
) else (
    echo ✅ Node.js is working!
)
echo.

echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found
    goto :error
) else (
    echo ✅ npm is working!
)
echo.

echo ========================================
echo     ALL PREREQUISITES VERIFIED! ✅
echo ========================================
echo.
echo You can now run:
echo   1. install.bat (for Laravel backend)
echo   2. cd frontend && install-frontend.bat (for React frontend)
echo.
goto :end

:error
echo.
echo ========================================
echo     SETUP INCOMPLETE ❌
echo ========================================
echo.
echo Please install the missing components and try again.
echo.
echo Quick Setup Options:
echo 1. XAMPP: https://www.apachefriends.org/
echo 2. Composer: https://getcomposer.org/
echo 3. Node.js: https://nodejs.org/
echo.

:end
pause
