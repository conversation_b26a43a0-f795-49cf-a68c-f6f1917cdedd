@echo off
echo ========================================
echo    ABATTOIR SALEM FRONTEND SETUP
echo         React Application
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    echo Or run verify-setup.bat to check all prerequisites
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    echo Please install Node.js which includes npm
    pause
    exit /b 1
)

echo [1/3] Installing React dependencies...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Try running: npm cache clean --force
    pause
    exit /b 1
)

echo [2/3] Creating environment file...
if not exist .env (
    echo REACT_APP_API_URL=http://localhost:8000/api > .env
    echo REACT_APP_APP_NAME=Abattoir Salem >> .env
    echo REACT_APP_VERSION=1.0.0 >> .env
    echo REACT_APP_COMPANY_NAME=Abattoir Salem >> .env
    echo Environment file created.
) else (
    echo Environment file already exists.
)

echo [3/3] Starting development server...
echo.
echo ========================================
echo     FRONTEND READY! 🚀
echo ========================================
echo.
echo Frontend running at: http://localhost:3000
echo.
echo Make sure the Laravel backend is running on:
echo http://localhost:8000
echo.
echo Demo Accounts:
echo   Admin: <EMAIL> / admin123
echo   Magasinier: <EMAIL> / ahmed123
echo   Commercial: <EMAIL> / fatima123
echo   Livreur: <EMAIL> / mohamed123
echo.
echo Press Ctrl+C to stop the server
echo.

npm start
