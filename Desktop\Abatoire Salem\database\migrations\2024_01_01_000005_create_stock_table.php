<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('reception_id')->constrained()->onDelete('cascade');
            $table->decimal('current_weight_kg', 10, 3); // Current available weight
            $table->decimal('initial_weight_kg', 10, 3); // Initial weight from reception
            $table->decimal('reserved_weight_kg', 10, 3)->default(0); // Reserved for orders
            $table->decimal('minimum_alert_kg', 8, 3)->default(10); // Alert threshold
            $table->boolean('is_available')->default(true);
            $table->timestamps();
            
            // Ensure unique stock entry per product per reception
            $table->unique(['product_id', 'reception_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock');
    }
};
