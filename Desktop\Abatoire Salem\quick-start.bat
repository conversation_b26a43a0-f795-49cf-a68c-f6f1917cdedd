@echo off
echo ========================================
echo    ABATTOIR SALEM QUICK START
echo ========================================
echo.

echo [1/4] Installing Laravel dependencies...
call composer install
if %errorlevel% neq 0 (
    echo ERROR: Composer install failed
    pause
    exit /b 1
)

echo [2/4] Setting up Laravel environment...
if not exist .env (
    copy .env.example .env
    php artisan key:generate
    php artisan jwt:secret --force
)

echo [3/4] Setting up database...
php artisan migrate:fresh --seed --force
if %errorlevel% neq 0 (
    echo ERROR: Database setup failed
    pause
    exit /b 1
)

echo [4/4] Starting Laravel server...
echo.
echo ========================================
echo     BACKEND READY! 🚀
echo ========================================
echo.
echo Backend running at: http://127.0.0.1:8000
echo.
echo Demo Accounts:
echo   Admin: <EMAIL> / admin123
echo   Magasinier: <EMAIL> / ahmed123
echo   Commercial: <EMAIL> / fatima123
echo   Livreur: <EMAIL> / mohamed123
echo.
echo Now open a NEW terminal and run:
echo   cd frontend
echo   install-frontend.bat
echo.
echo Press Ctrl+C to stop the server
echo.

php artisan serve
