# Abattoir Salem - Frontend React

Interface utilisateur moderne et responsive pour le système de gestion d'abattoir.

## 🚀 Fonctionnalités

### ✅ **Implémentées**
- **Authentification JWT** avec gestion des rôles
- **Dashboard en temps réel** avec statistiques et graphiques
- **Module Réception** complet avec CRUD et PDF
- **Module Stock** avec alertes et ajustements
- **Layout responsive** avec navigation adaptative
- **Système de notifications** en temps réel
- **Gestion des erreurs** et loading states
- **Thème professionnel** Ant Design personnalisé

### 🔄 **En développement**
- Modules Commandes, Clients, Livraisons, Paiements
- Module Traçabilité complet
- Rapports et analyses avancées
- Gestion des utilisateurs

## 🛠️ Technologies

- **React 18** - Framework frontend
- **Ant Design 5** - Composants UI
- **React Query** - Gestion des données
- **React Router 6** - Navigation
- **Recharts** - Graphiques et visualisations
- **Axios** - Client HTTP
- **Day.js** - Gestion des dates

## 📦 Installation

### Prérequis
- Node.js 16+
- npm ou yarn
- Backend Laravel en cours d'exécution

### Installation automatique

**Windows:**
```bash
cd frontend
install-frontend.bat
```

**Linux/Mac:**
```bash
cd frontend
chmod +x install-frontend.sh
./install-frontend.sh
```

### Installation manuelle

1. **Installer les dépendances**
   ```bash
   cd frontend
   npm install
   ```

2. **Configuration**
   ```bash
   cp .env.example .env
   # Modifier .env selon vos besoins
   ```

3. **Démarrer en développement**
   ```bash
   npm start
   ```

4. **Build de production**
   ```bash
   npm run build
   ```

## 🔧 Configuration

### Variables d'environnement (.env)

```env
# API Backend
REACT_APP_API_URL=http://localhost:8000/api

# Application
REACT_APP_APP_NAME=Abattoir Salem
REACT_APP_VERSION=1.0.0

# Entreprise
REACT_APP_COMPANY_NAME=Abattoir Salem
REACT_APP_COMPANY_ADDRESS=Algérie
REACT_APP_COMPANY_PHONE=+213 XXX XXX XXX
REACT_APP_COMPANY_EMAIL=<EMAIL>
```

## 🏗️ Structure du projet

```
frontend/
├── public/
│   ├── index.html          # Template HTML principal
│   └── manifest.json       # Configuration PWA
├── src/
│   ├── components/         # Composants réutilisables
│   │   ├── Layout/         # Layout principal et navigation
│   │   └── ProtectedRoute.js
│   ├── contexts/           # Contextes React
│   │   ├── AuthContext.js  # Authentification
│   │   └── NotificationContext.js
│   ├── pages/              # Pages de l'application
│   │   ├── Auth/           # Connexion
│   │   ├── Dashboard/      # Tableau de bord
│   │   ├── Reception/      # Gestion réceptions
│   │   ├── Stock/          # Gestion stock
│   │   └── ...             # Autres modules
│   ├── services/           # Services API
│   │   └── api.js          # Configuration Axios
│   ├── App.js              # Composant principal
│   └── index.js            # Point d'entrée
├── package.json            # Dépendances
└── README.md              # Documentation
```

## 🎨 Interface utilisateur

### Thème et design
- **Couleurs principales:** #2c3e50 (bleu foncé), #27ae60 (vert)
- **Design responsive** pour desktop et tablette
- **Navigation adaptative** avec sidebar collapsible
- **Composants cohérents** avec Ant Design

### Fonctionnalités UX
- **Loading states** sur toutes les actions
- **Messages de confirmation** pour actions critiques
- **Notifications toast** pour feedback utilisateur
- **Gestion d'erreurs** avec messages explicites
- **Pagination** et filtres sur toutes les listes

## 🔐 Authentification et sécurité

### Système d'authentification
- **JWT tokens** avec refresh automatique
- **Rôles utilisateurs:** Admin, Magasinier, Commercial, Livreur
- **Routes protégées** selon les permissions
- **Session persistante** avec localStorage

### Comptes de démonstration
- **Admin:** <EMAIL> / admin123
- **Magasinier:** <EMAIL> / ahmed123
- **Commercial:** <EMAIL> / fatima123
- **Livreur:** <EMAIL> / mohamed123

## 📊 Modules implémentés

### 1. Dashboard
- Statistiques en temps réel
- Graphiques des 7 derniers jours
- Top clients du mois
- Alertes stock faible
- Activités récentes

### 2. Réception
- Liste des réceptions avec filtres
- Création/modification de réceptions
- Génération automatique de numéros de lot
- Statistiques de réception
- Export PDF des bons de réception

### 3. Stock
- Vue d'ensemble du stock en temps réel
- Alertes stock faible/critique
- Ajustements de stock avec traçabilité
- Statistiques et valorisation
- Filtres par produit et statut

## 🚀 Scripts disponibles

```bash
# Développement
npm start              # Serveur de développement (port 3000)

# Production
npm run build          # Build optimisé pour production
npm run serve          # Servir le build de production

# Tests et qualité
npm test               # Tests unitaires
npm run build:prod     # Build avec optimisations avancées
```

## 🌐 Déploiement

### Développement local
1. Backend Laravel sur `http://localhost:8000`
2. Frontend React sur `http://localhost:3000`
3. Proxy automatique configuré vers l'API

### Production
1. Build de production: `npm run build`
2. Servir le dossier `build/` avec un serveur web
3. Configurer les variables d'environnement
4. S'assurer que l'API backend est accessible

## 🔄 Intégration avec le backend

### API Endpoints utilisés
- `POST /api/login` - Authentification
- `GET /api/dashboard/stats` - Statistiques dashboard
- `GET /api/receptions` - Liste des réceptions
- `POST /api/receptions` - Créer une réception
- `GET /api/stock` - Gestion du stock
- Et 40+ autres endpoints...

### Gestion des erreurs
- Intercepteurs Axios pour gestion centralisée
- Messages d'erreur traduits en français
- Retry automatique sur erreurs réseau
- Redirection automatique si session expirée

## 📱 Responsive Design

- **Desktop:** Layout complet avec sidebar
- **Tablette:** Navigation adaptée, sidebar collapsible
- **Mobile:** Menu drawer, interface optimisée
- **Impression:** Styles dédiés pour les documents

## 🎯 Prochaines étapes

1. **Finaliser les modules restants** (Commandes, Clients, etc.)
2. **Tests unitaires** avec Jest et React Testing Library
3. **Optimisations performance** (lazy loading, memoization)
4. **PWA** pour utilisation hors ligne
5. **Déploiement réseau local** multi-postes

## 📞 Support

Pour toute question ou problème:
- Vérifier que le backend Laravel fonctionne
- Consulter la console navigateur pour les erreurs
- S'assurer que les variables d'environnement sont correctes
