import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { LoadingOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children, roles = [] }) => {
  const { isAuthenticated, loading, user, hasAnyRole } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <Spin 
          size="large" 
          indicator={<LoadingOutlined style={{ fontSize: 48, color: '#2c3e50' }} spin />}
        />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access if roles are specified
  if (roles.length > 0 && !hasAnyRole(roles)) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        padding: '20px'
      }}>
        <Result
          status="403"
          title="403"
          subTitle="Désolé, vous n'avez pas l'autorisation d'accéder à cette page."
          icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
          extra={
            <div>
              <p style={{ marginBottom: '16px', color: '#666' }}>
                <strong>Votre rôle:</strong> {user?.role || 'Non défini'}
              </p>
              <p style={{ marginBottom: '16px', color: '#666' }}>
                <strong>Rôles requis:</strong> {roles.join(', ')}
              </p>
              <Button 
                type="primary" 
                onClick={() => window.history.back()}
                style={{ marginRight: '8px' }}
              >
                Retour
              </Button>
              <Button onClick={() => window.location.href = '/dashboard'}>
                Tableau de bord
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Render children if all checks pass
  return children;
};

export default ProtectedRoute;
