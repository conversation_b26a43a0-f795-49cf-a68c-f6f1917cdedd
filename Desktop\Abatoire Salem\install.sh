#!/bin/bash

echo "========================================"
echo "   ABATTOIR SALEM MANAGEMENT SYSTEM"
echo "        Installation Script"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo -e "${RED}ERROR: PHP is not installed${NC}"
    echo "Please install PHP 8.1 or higher"
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
if [[ $(echo "$PHP_VERSION 8.1" | awk '{print ($1 < $2)}') == 1 ]]; then
    echo -e "${RED}ERROR: PHP version $PHP_VERSION is too old${NC}"
    echo "Please install PHP 8.1 or higher"
    exit 1
fi

# Check if Composer is installed
if ! command -v composer &> /dev/null; then
    echo -e "${RED}ERROR: Composer is not installed${NC}"
    echo "Please install Composer from https://getcomposer.org/"
    exit 1
fi

echo -e "${GREEN}[1/8] Installing PHP dependencies...${NC}"
composer install --no-dev --optimize-autoloader
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to install dependencies${NC}"
    exit 1
fi

echo -e "${GREEN}[2/8] Setting up environment file...${NC}"
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Environment file created. Please configure your database settings."
else
    echo "Environment file already exists."
fi

echo -e "${GREEN}[3/8] Generating application key...${NC}"
php artisan key:generate
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to generate application key${NC}"
    exit 1
fi

echo -e "${GREEN}[4/8] Generating JWT secret...${NC}"
php artisan jwt:secret
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to generate JWT secret${NC}"
    exit 1
fi

echo -e "${GREEN}[5/8] Creating storage directories...${NC}"
mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/app/public

# Set proper permissions
chmod -R 775 storage
chmod -R 775 bootstrap/cache

echo -e "${GREEN}[6/8] Setting up database...${NC}"
echo "Please make sure your database is created and configured in .env file"
echo "Press Enter to continue with database migration..."
read

php artisan migrate --force
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Database migration failed${NC}"
    echo "Please check your database configuration in .env file"
    exit 1
fi

echo -e "${GREEN}[7/8] Seeding database with initial data...${NC}"
php artisan db:seed --force
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}WARNING: Database seeding failed, but installation can continue${NC}"
fi

echo -e "${GREEN}[8/8] Setting up storage link...${NC}"
php artisan storage:link
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}WARNING: Storage link creation failed${NC}"
fi

echo
echo "========================================"
echo -e "${GREEN}     INSTALLATION COMPLETED!${NC}"
echo "========================================"
echo
echo "Default users created:"
echo "- Admin: <EMAIL> / admin123"
echo "- Storekeeper: <EMAIL> / ahmed123"
echo "- Salesperson: <EMAIL> / fatima123"
echo "- Delivery: <EMAIL> / mohamed123"
echo
echo "To start the development server, run:"
echo -e "${GREEN}php artisan serve${NC}"
echo
echo "The application will be available at:"
echo -e "${GREEN}http://localhost:8000${NC}"
echo
echo "For production deployment, configure your web server"
echo "to point to the 'public' directory."
echo
