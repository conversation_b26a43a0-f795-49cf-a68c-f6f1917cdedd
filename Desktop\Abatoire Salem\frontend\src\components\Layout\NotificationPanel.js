import React from 'react';
import {
  Drawer,
  List,
  Typography,
  Button,
  Space,
  Badge,
  Empty,
  Divider,
  Tag,
} from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNotifications } from '../../contexts/NotificationContext';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

const NotificationPanel = ({ visible, onClose }) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
  } = useNotifications();

  const getNotificationIcon = (type) => {
    const icons = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      info: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      warning: <WarningOutlined style={{ color: '#faad14' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
    };
    return icons[type] || icons.info;
  };

  const getCategoryColor = (category) => {
    const colors = {
      stock: 'orange',
      orders: 'blue',
      payments: 'green',
      deliveries: 'purple',
      system: 'default',
    };
    return colors[category] || 'default';
  };

  const getCategoryLabel = (category) => {
    const labels = {
      stock: 'Stock',
      orders: 'Commandes',
      payments: 'Paiements',
      deliveries: 'Livraisons',
      system: 'Système',
    };
    return labels[category] || category;
  };

  const handleNotificationClick = (notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    // Handle navigation based on notification type
    if (notification.data) {
      // You can add navigation logic here based on notification category
      console.log('Navigate to:', notification.category, notification.data);
    }
  };

  return (
    <Drawer
      title={
        <Space>
          <BellOutlined />
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Badge count={unreadCount} size="small" />
          )}
        </Space>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
      extra={
        <Space>
          {unreadCount > 0 && (
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              onClick={markAllAsRead}
            >
              Tout marquer lu
            </Button>
          )}
          {notifications.length > 0 && (
            <Button
              type="link"
              size="small"
              icon={<DeleteOutlined />}
              onClick={clearAll}
              danger
            >
              Tout effacer
            </Button>
          )}
        </Space>
      }
    >
      {notifications.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Aucune notification"
        />
      ) : (
        <List
          dataSource={notifications}
          renderItem={(notification) => (
            <List.Item
              style={{
                backgroundColor: notification.read ? 'transparent' : '#f6ffed',
                border: notification.read ? 'none' : '1px solid #b7eb8f',
                borderRadius: '6px',
                marginBottom: '8px',
                padding: '12px',
                cursor: 'pointer',
              }}
              onClick={() => handleNotificationClick(notification)}
              actions={[
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    removeNotification(notification.id);
                  }}
                  danger
                />,
              ]}
            >
              <List.Item.Meta
                avatar={getNotificationIcon(notification.type)}
                title={
                  <Space>
                    <Text strong={!notification.read}>
                      {notification.title}
                    </Text>
                    {!notification.read && (
                      <Badge status="processing" />
                    )}
                  </Space>
                }
                description={
                  <div>
                    <Text type="secondary">
                      {notification.message}
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      <Space>
                        <Tag color={getCategoryColor(notification.category)}>
                          {getCategoryLabel(notification.category)}
                        </Tag>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {dayjs(notification.timestamp).fromNow()}
                        </Text>
                      </Space>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
      
      {notifications.length > 10 && (
        <>
          <Divider />
          <div style={{ textAlign: 'center' }}>
            <Button type="link">
              Voir toutes les notifications
            </Button>
          </div>
        </>
      )}
    </Drawer>
  );
};

export default NotificationPanel;
