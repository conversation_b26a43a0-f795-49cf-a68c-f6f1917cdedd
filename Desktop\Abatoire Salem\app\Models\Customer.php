<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'address',
        'credit_limit',
        'max_unpaid_orders',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get customer's orders
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get customer's payments
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get unpaid orders count
     */
    public function getUnpaidOrdersCountAttribute()
    {
        return $this->orders()->where('payment_status', 'unpaid')->count();
    }

    /**
     * Get total unpaid amount
     */
    public function getTotalUnpaidAmountAttribute()
    {
        return $this->orders()
            ->whereIn('payment_status', ['unpaid', 'partial'])
            ->sum('total_amount') - $this->orders()
            ->whereIn('payment_status', ['unpaid', 'partial'])
            ->sum('paid_amount');
    }

    /**
     * Check if customer can place new order (credit limit check)
     */
    public function canPlaceOrder(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check unpaid orders limit
        if ($this->unpaid_orders_count >= $this->max_unpaid_orders) {
            return false;
        }

        return true;
    }

    /**
     * Get customer's credit status
     */
    public function getCreditStatusAttribute(): string
    {
        $unpaidAmount = $this->total_unpaid_amount;
        
        if ($unpaidAmount == 0) {
            return 'good';
        } elseif ($unpaidAmount <= $this->credit_limit * 0.8) {
            return 'warning';
        } else {
            return 'critical';
        }
    }

    /**
     * Scope for active customers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for customers with unpaid orders
     */
    public function scopeWithUnpaidOrders($query)
    {
        return $query->whereHas('orders', function ($q) {
            $q->whereIn('payment_status', ['unpaid', 'partial']);
        });
    }
}
