@echo off
echo ========================================
echo    ABATTOIR SALEM MANAGEMENT SYSTEM
echo         Installation Script
echo ========================================
echo.

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP 8.1 or higher
    pause
    exit /b 1
)

REM Check if Composer is installed
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed or not in PATH
    echo Please install Composer from https://getcomposer.org/
    pause
    exit /b 1
)

echo [1/8] Installing PHP dependencies...
composer install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [2/8] Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created. Please configure your database settings.
) else (
    echo Environment file already exists.
)

echo [3/8] Generating application key...
php artisan key:generate
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate application key
    pause
    exit /b 1
)

echo [4/8] Generating JWT secret...
php artisan jwt:secret
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate JWT secret
    pause
    exit /b 1
)

echo [5/8] Creating storage directories...
if not exist storage\logs mkdir storage\logs
if not exist storage\framework\cache mkdir storage\framework\cache
if not exist storage\framework\sessions mkdir storage\framework\sessions
if not exist storage\framework\views mkdir storage\framework\views
if not exist storage\app\public mkdir storage\app\public

echo [6/8] Setting up database...
echo Please make sure your database is created and configured in .env file
echo Press any key to continue with database migration...
pause >nul

php artisan migrate --force
if %errorlevel% neq 0 (
    echo ERROR: Database migration failed
    echo Please check your database configuration in .env file
    pause
    exit /b 1
)

echo [7/8] Seeding database with initial data...
php artisan db:seed --force
if %errorlevel% neq 0 (
    echo WARNING: Database seeding failed, but installation can continue
)

echo [8/8] Setting up storage link...
php artisan storage:link
if %errorlevel% neq 0 (
    echo WARNING: Storage link creation failed
)

echo.
echo ========================================
echo     INSTALLATION COMPLETED!
echo ========================================
echo.
echo Default users created:
echo - Admin: <EMAIL> / admin123
echo - Storekeeper: <EMAIL> / ahmed123
echo - Salesperson: <EMAIL> / fatima123
echo - Delivery: <EMAIL> / mohamed123
echo.
echo To start the development server, run:
echo php artisan serve
echo.
echo The application will be available at:
echo http://localhost:8000
echo.
echo For production deployment, configure your web server
echo to point to the 'public' directory.
echo.
pause
