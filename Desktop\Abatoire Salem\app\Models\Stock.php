<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stock extends Model
{
    use HasFactory;

    protected $table = 'stock';

    protected $fillable = [
        'product_id',
        'reception_id',
        'current_weight_kg',
        'initial_weight_kg',
        'reserved_weight_kg',
        'minimum_alert_kg',
        'is_available',
    ];

    protected $casts = [
        'current_weight_kg' => 'decimal:3',
        'initial_weight_kg' => 'decimal:3',
        'reserved_weight_kg' => 'decimal:3',
        'minimum_alert_kg' => 'decimal:3',
        'is_available' => 'boolean',
    ];

    /**
     * Get available weight (current - reserved)
     */
    public function getAvailableWeightAttribute()
    {
        return $this->current_weight_kg - $this->reserved_weight_kg;
    }

    /**
     * Check if stock is low
     */
    public function isLowStock(): bool
    {
        return $this->available_weight <= $this->minimum_alert_kg;
    }

    /**
     * Check if stock is out
     */
    public function isOutOfStock(): bool
    {
        return $this->available_weight <= 0;
    }

    /**
     * Reserve stock for an order
     */
    public function reserve(float $quantity, int $userId, string $reason = null): bool
    {
        if ($this->available_weight < $quantity) {
            return false;
        }

        $this->reserved_weight_kg += $quantity;
        $this->save();

        // Log the reservation
        StockMovement::create([
            'stock_id' => $this->id,
            'user_id' => $userId,
            'movement_type' => 'reservation',
            'quantity_kg' => $quantity,
            'balance_after' => $this->current_weight_kg,
            'reason' => $reason ?? 'Réservation stock',
            'movement_date' => now(),
        ]);

        return true;
    }

    /**
     * Unreserve stock (cancel reservation)
     */
    public function unreserve(float $quantity, int $userId, string $reason = null): bool
    {
        if ($this->reserved_weight_kg < $quantity) {
            return false;
        }

        $this->reserved_weight_kg -= $quantity;
        $this->save();

        // Log the unreservation
        StockMovement::create([
            'stock_id' => $this->id,
            'user_id' => $userId,
            'movement_type' => 'unreservation',
            'quantity_kg' => -$quantity,
            'balance_after' => $this->current_weight_kg,
            'reason' => $reason ?? 'Annulation réservation',
            'movement_date' => now(),
        ]);

        return true;
    }

    /**
     * Consume stock (actual usage)
     */
    public function consume(float $quantity, int $userId, string $referenceType = null, int $referenceId = null, string $reason = null): bool
    {
        if ($this->current_weight_kg < $quantity) {
            return false;
        }

        $this->current_weight_kg -= $quantity;
        
        // Also reduce reservation if it exists
        if ($this->reserved_weight_kg >= $quantity) {
            $this->reserved_weight_kg -= $quantity;
        } else {
            $this->reserved_weight_kg = 0;
        }

        $this->save();

        // Log the consumption
        StockMovement::create([
            'stock_id' => $this->id,
            'user_id' => $userId,
            'movement_type' => 'out',
            'quantity_kg' => -$quantity,
            'balance_after' => $this->current_weight_kg,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'reason' => $reason ?? 'Consommation stock',
            'movement_date' => now(),
        ]);

        return true;
    }

    /**
     * Relationships
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function reception()
    {
        return $this->belongsTo(Reception::class);
    }

    public function movements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scopes
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)
                    ->where('current_weight_kg', '>', 0);
    }

    public function scopeLowStock($query)
    {
        return $query->whereRaw('(current_weight_kg - reserved_weight_kg) <= minimum_alert_kg');
    }

    public function scopeOutOfStock($query)
    {
        return $query->whereRaw('(current_weight_kg - reserved_weight_kg) <= 0');
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }
}
