import React from 'react';
import { Card, List, Avatar, Typography, Timeline, Empty, Spin, Tag } from 'antd';
import {
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { dashboardAPI } from '../../../services/api';
import dayjs from 'dayjs';

const { Text } = Typography;

const RecentActivities = () => {
  const { data, isLoading } = useQuery(
    'recentActivities',
    () => dashboardAPI.getRecentActivities({ limit: 10, days: 3 }),
    {
      refetchInterval: 60000, // Refetch every minute
    }
  );

  const activities = data?.data?.data || [];

  const getActionIcon = (action) => {
    const icons = {
      created: <PlusOutlined style={{ color: '#52c41a' }} />,
      updated: <EditOutlined style={{ color: '#1890ff' }} />,
      deleted: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
      login: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      logout: <ClockCircleOutlined style={{ color: '#faad14' }} />,
    };
    return icons[action] || <UserOutlined />;
  };

  const getActionColor = (action) => {
    const colors = {
      created: 'green',
      updated: 'blue',
      deleted: 'red',
      login: 'green',
      logout: 'orange',
    };
    return colors[action] || 'default';
  };

  const getActionLabel = (action) => {
    const labels = {
      created: 'Créé',
      updated: 'Modifié',
      deleted: 'Supprimé',
      login: 'Connexion',
      logout: 'Déconnexion',
    };
    return labels[action] || action;
  };

  const getModelTypeLabel = (modelType) => {
    const labels = {
      Order: 'Commande',
      Customer: 'Client',
      Reception: 'Réception',
      Stock: 'Stock',
      Payment: 'Paiement',
      User: 'Utilisateur',
      DeliveryNote: 'Livraison',
      PreparationSlip: 'Préparation',
    };
    return labels[modelType] || modelType;
  };

  return (
    <Card
      title="Activités Récentes"
      className="chart-container"
    >
      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : activities.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Aucune activité récente"
        />
      ) : (
        <Timeline
          items={activities.map((activity, index) => ({
            key: activity.id,
            dot: getActionIcon(activity.action),
            children: (
              <div style={{ marginBottom: index === activities.length - 1 ? 0 : '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                  <Text strong>{activity.user || 'Système'}</Text>
                  <Tag color={getActionColor(activity.action)} size="small">
                    {getActionLabel(activity.action)}
                  </Tag>
                  {activity.model_type && (
                    <Tag size="small">
                      {getModelTypeLabel(activity.model_type)}
                    </Tag>
                  )}
                </div>
                
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary">
                    {activity.description}
                  </Text>
                </div>
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {activity.time_ago}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    {dayjs(activity.created_at).format('DD/MM/YYYY HH:mm')}
                  </Text>
                </div>
              </div>
            ),
          }))}
        />
      )}
    </Card>
  );
};

export default RecentActivities;
