<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Reception\ReceptionController;
use App\Http\Controllers\Stock\StockController;
use App\Http\Controllers\Commande\OrderController;
use App\Http\Controllers\Credit\CreditController;
use App\Http\Controllers\Livraison\DeliveryController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Tracabilite\TraceabilityController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:api')->group(function () {
    
    // Authentication routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/me', [AuthController::class, 'me']);
    Route::post('/change-password', [AuthController::class, 'changePassword']);
    
    // User management (Admin only)
    Route::middleware('role:admin')->group(function () {
        Route::post('/register', [AuthController::class, 'register']);
        Route::get('/users', [AuthController::class, 'users']);
        Route::put('/users/{user}', [AuthController::class, 'updateUser']);
        Route::delete('/users/{user}', [AuthController::class, 'deleteUser']);
    });

    // Reception routes (Storekeeper, Admin)
    Route::middleware('role:storekeeper,admin')->group(function () {
        Route::get('/receptions', [ReceptionController::class, 'index']);
        Route::post('/receptions', [ReceptionController::class, 'store']);
        Route::get('/receptions/{reception}', [ReceptionController::class, 'show']);
        Route::put('/receptions/{reception}', [ReceptionController::class, 'update']);
        Route::delete('/receptions/{reception}', [ReceptionController::class, 'destroy']);
        Route::get('/receptions/{reception}/pdf', [ReceptionController::class, 'generatePDF']);
    });

    // Stock routes (All authenticated users can view, Storekeeper/Admin can modify)
    Route::get('/stock', [StockController::class, 'index']);
    Route::get('/stock/{stock}', [StockController::class, 'show']);
    Route::get('/stock/low-stock', [StockController::class, 'lowStock']);
    Route::get('/stock/movements', [StockController::class, 'movements']);
    Route::get('/stock/{stock}/movements', [StockController::class, 'stockMovements']);
    
    Route::middleware('role:storekeeper,admin')->group(function () {
        Route::post('/stock/adjustment', [StockController::class, 'adjustment']);
        Route::put('/stock/{stock}/alert', [StockController::class, 'updateAlert']);
    });

    // Order routes (Salesperson, Admin can create/modify, others can view)
    Route::get('/orders', [OrderController::class, 'index']);
    Route::get('/orders/{order}', [OrderController::class, 'show']);
    Route::get('/orders/{order}/pdf', [OrderController::class, 'generatePDF']);
    
    Route::middleware('role:salesperson,admin')->group(function () {
        Route::post('/orders', [OrderController::class, 'store']);
        Route::put('/orders/{order}', [OrderController::class, 'update']);
        Route::post('/orders/{order}/confirm', [OrderController::class, 'confirm']);
        Route::post('/orders/{order}/cancel', [OrderController::class, 'cancel']);
    });

    // Preparation routes (Storekeeper, Admin)
    Route::middleware('role:storekeeper,admin')->group(function () {
        Route::get('/preparations', [OrderController::class, 'preparations']);
        Route::post('/orders/{order}/prepare', [OrderController::class, 'startPreparation']);
        Route::post('/orders/{order}/complete-preparation', [OrderController::class, 'completePreparation']);
        Route::get('/preparations/{preparation}/pdf', [OrderController::class, 'generatePreparationPDF']);
    });

    // Delivery routes (Delivery, Admin)
    Route::middleware('role:delivery,admin')->group(function () {
        Route::get('/deliveries', [DeliveryController::class, 'index']);
        Route::post('/orders/{order}/deliver', [DeliveryController::class, 'startDelivery']);
        Route::post('/deliveries/{delivery}/complete', [DeliveryController::class, 'completeDelivery']);
        Route::post('/deliveries/{delivery}/return', [DeliveryController::class, 'returnDelivery']);
        Route::get('/deliveries/{delivery}/pdf', [DeliveryController::class, 'generatePDF']);
    });

    // Credit management routes (Salesperson, Admin)
    Route::middleware('role:salesperson,admin')->group(function () {
        Route::get('/customers', [CreditController::class, 'customers']);
        Route::post('/customers', [CreditController::class, 'createCustomer']);
        Route::put('/customers/{customer}', [CreditController::class, 'updateCustomer']);
        Route::get('/customers/{customer}/credit-status', [CreditController::class, 'creditStatus']);
        Route::get('/customers/{customer}/orders', [CreditController::class, 'customerOrders']);
        Route::post('/payments', [CreditController::class, 'recordPayment']);
        Route::get('/payments', [CreditController::class, 'payments']);
        Route::get('/customers/{customer}/payments', [CreditController::class, 'customerPayments']);
    });

    // Dashboard routes (All authenticated users)
    Route::get('/dashboard/stats', [DashboardController::class, 'stats']);
    Route::get('/dashboard/daily-stats', [DashboardController::class, 'dailyStats']);
    Route::get('/dashboard/monthly-stats', [DashboardController::class, 'monthlyStats']);
    Route::get('/dashboard/top-customers', [DashboardController::class, 'topCustomers']);
    Route::get('/dashboard/low-stock-alerts', [DashboardController::class, 'lowStockAlerts']);
    Route::get('/dashboard/recent-activities', [DashboardController::class, 'recentActivities']);

    // Traceability routes (All authenticated users)
    Route::get('/traceability/search', [TraceabilityController::class, 'search']);
    Route::get('/traceability/lot/{lotNumber}', [TraceabilityController::class, 'byLot']);
    Route::get('/traceability/product/{product}', [TraceabilityController::class, 'byProduct']);
    Route::get('/traceability/customer/{customer}', [TraceabilityController::class, 'byCustomer']);
    Route::get('/traceability/date-range', [TraceabilityController::class, 'byDateRange']);

    // Products routes (All can view, Admin can modify)
    Route::get('/products', function () {
        return response()->json(\App\Models\Product::active()->get());
    });
    
    Route::middleware('role:admin')->group(function () {
        Route::post('/products', function (Request $request) {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'animal_type' => 'required|string|max:100',
                'description' => 'nullable|string',
                'unit_price' => 'required|numeric|min:0',
                'average_weight' => 'nullable|numeric|min:0',
            ]);
            
            return response()->json(\App\Models\Product::create($validated), 201);
        });
        
        Route::put('/products/{product}', function (Request $request, \App\Models\Product $product) {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'animal_type' => 'required|string|max:100',
                'description' => 'nullable|string',
                'unit_price' => 'required|numeric|min:0',
                'average_weight' => 'nullable|numeric|min:0',
                'is_active' => 'boolean',
            ]);
            
            $product->update($validated);
            return response()->json($product);
        });
    });

    // Export routes (Admin only)
    Route::middleware('role:admin')->group(function () {
        Route::get('/export/orders', [DashboardController::class, 'exportOrders']);
        Route::get('/export/customers', [DashboardController::class, 'exportCustomers']);
        Route::get('/export/stock', [DashboardController::class, 'exportStock']);
        Route::get('/export/receptions', [DashboardController::class, 'exportReceptions']);
    });
});
