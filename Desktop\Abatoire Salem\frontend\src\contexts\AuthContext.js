import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import api from '../services/api';

// Initial state
const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  loading: true,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  UPDATE_USER: 'UPDATE_USER',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        loading: true,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          
          // Verify token with backend
          const response = await api.get('/me');
          
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              user: response.data.user,
              token: token,
            },
          });
        } catch (error) {
          // Token is invalid, remove it
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
        }
      } else {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const response = await api.post('/login', credentials);
      
      if (response.data.success) {
        const { access_token, user } = response.data;
        
        // Store token
        localStorage.setItem('token', access_token);
        
        // Set token in API headers
        api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: user,
            token: access_token,
          },
        });

        message.success('Connexion réussie');
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Erreur de connexion');
      }
    } catch (error) {
      dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
      
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Erreur de connexion';
      
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout endpoint
      await api.post('/logout');
    } catch (error) {
      // Even if logout fails on server, we still logout locally
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      message.success('Déconnexion réussie');
    }
  };

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      const response = await api.post('/change-password', passwordData);
      
      if (response.data.success) {
        message.success('Mot de passe modifié avec succès');
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Erreur lors du changement de mot de passe');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Erreur lors du changement de mot de passe';
      
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Update user profile
  const updateProfile = async (userData) => {
    try {
      const response = await api.put('/profile', userData);
      
      if (response.data.success) {
        dispatch({
          type: AUTH_ACTIONS.UPDATE_USER,
          payload: response.data.user,
        });
        
        message.success('Profil mis à jour avec succès');
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Erreur lors de la mise à jour du profil');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Erreur lors de la mise à jour du profil';
      
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return state.user?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(state.user?.role);
  };

  // Check if user can access specific feature
  const canAccess = (feature) => {
    const permissions = {
      reception: ['admin', 'storekeeper'],
      stock: ['admin', 'storekeeper', 'salesperson', 'delivery'],
      orders: ['admin', 'salesperson'],
      customers: ['admin', 'salesperson'],
      deliveries: ['admin', 'delivery'],
      payments: ['admin', 'salesperson'],
      traceability: ['admin', 'storekeeper', 'salesperson', 'delivery'],
      reports: ['admin', 'salesperson'],
      users: ['admin'],
      settings: ['admin', 'storekeeper', 'salesperson', 'delivery'],
    };

    return permissions[feature]?.includes(state.user?.role) || false;
  };

  const value = {
    ...state,
    login,
    logout,
    changePassword,
    updateProfile,
    hasRole,
    hasAnyRole,
    canAccess,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
