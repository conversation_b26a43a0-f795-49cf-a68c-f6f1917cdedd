<?php

namespace App\Http\Controllers\Livraison;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Order;
use App\Models\DeliveryNote;
use App\Models\ActivityLog;
use Carbon\Carbon;

class DeliveryController extends Controller
{
    /**
     * Display a listing of deliveries
     */
    public function index(Request $request)
    {
        $query = DeliveryNote::with([
            'order.customer', 
            'order.orderItems.product', 
            'preparationSlip', 
            'deliveredBy'
        ]);

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->byStatus($request->status);
        }

        if ($request->has('delivered_by') && $request->delivered_by) {
            $query->byDeliverer($request->delivered_by);
        }

        if ($request->has('customer_id') && $request->customer_id) {
            $query->whereHas('order', function($q) use ($request) {
                $q->where('customer_id', $request->customer_id);
            });
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('delivery_date', [$request->start_date, $request->end_date]);
        }

        if ($request->has('today') && $request->boolean('today')) {
            $query->today();
        }

        if ($request->has('overdue') && $request->boolean('overdue')) {
            $query->overdue();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'delivery_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $deliveries = $query->paginate($perPage);

        // Add calculated fields
        $deliveries->getCollection()->transform(function ($delivery) {
            $delivery->is_overdue = $delivery->isOverdue();
            $delivery->delivery_duration = $delivery->delivery_duration;
            return $delivery;
        });

        return response()->json([
            'success' => true,
            'data' => $deliveries,
            'message' => 'Deliveries retrieved successfully'
        ]);
    }

    /**
     * Start delivery for an order
     */
    public function startDelivery(Order $order)
    {
        // Check if order can be delivered
        if (!$order->canBeDelivered()) {
            return response()->json([
                'success' => false,
                'message' => 'Order cannot be delivered in current status'
            ], 400);
        }

        // Check if preparation is completed
        if (!$order->preparationSlip || $order->preparationSlip->status !== 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'Order preparation must be completed before delivery'
            ], 400);
        }

        // Check if delivery note already exists
        if ($order->deliveryNote) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery note already exists for this order'
            ], 400);
        }

        try {
            // Create delivery note
            $deliveryNote = DeliveryNote::create([
                'order_id' => $order->id,
                'preparation_slip_id' => $order->preparationSlip->id,
                'status' => 'pending',
                'delivery_date' => now(),
            ]);

            // Start delivery
            if (!$deliveryNote->startDelivery(auth()->id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to start delivery'
                ], 400);
            }

            // Log activity
            ActivityLog::logActivity(
                'created',
                "Started delivery for order {$order->order_number}",
                'DeliveryNote',
                $deliveryNote->id,
                null,
                $deliveryNote->toArray()
            );

            $deliveryNote->load([
                'order.customer', 
                'order.orderItems.product', 
                'preparationSlip', 
                'deliveredBy'
            ]);

            return response()->json([
                'success' => true,
                'data' => $deliveryNote,
                'message' => 'Delivery started successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start delivery: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete delivery
     */
    public function completeDelivery(Request $request, DeliveryNote $delivery)
    {
        if ($delivery->status !== 'in_transit') {
            return response()->json([
                'success' => false,
                'message' => 'Delivery is not in transit'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'customer_signature' => 'nullable|string|max:255',
            'delivery_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Complete delivery
            if (!$delivery->completeDelivery(
                $request->customer_signature,
                $request->delivery_notes
            )) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to complete delivery'
                ], 400);
            }

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Completed delivery for order {$delivery->order->order_number}",
                'DeliveryNote',
                $delivery->id,
                ['status' => 'in_transit'],
                ['status' => 'delivered']
            );

            $delivery->load([
                'order.customer', 
                'order.orderItems.product', 
                'preparationSlip', 
                'deliveredBy'
            ]);
            $delivery->delivery_duration = $delivery->delivery_duration;

            return response()->json([
                'success' => true,
                'data' => $delivery,
                'message' => 'Delivery completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete delivery: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Return delivery
     */
    public function returnDelivery(Request $request, DeliveryNote $delivery)
    {
        if (!in_array($delivery->status, ['in_transit', 'delivered'])) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery cannot be returned in current status'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'return_reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $oldStatus = $delivery->status;

            // Return delivery
            if (!$delivery->returnDelivery($request->return_reason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to return delivery'
                ], 400);
            }

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Returned delivery for order {$delivery->order->order_number}: {$request->return_reason}",
                'DeliveryNote',
                $delivery->id,
                ['status' => $oldStatus],
                ['status' => 'returned', 'return_reason' => $request->return_reason]
            );

            $delivery->load([
                'order.customer', 
                'order.orderItems.product', 
                'preparationSlip', 
                'deliveredBy'
            ]);

            return response()->json([
                'success' => true,
                'data' => $delivery,
                'message' => 'Delivery returned successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to return delivery: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get delivery statistics
     */
    public function statistics(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $stats = [
            'total_deliveries' => DeliveryNote::whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'completed_deliveries' => DeliveryNote::delivered()
                ->whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'pending_deliveries' => DeliveryNote::pending()->count(),
            'in_transit_deliveries' => DeliveryNote::inTransit()->count(),
            'returned_deliveries' => DeliveryNote::returned()
                ->whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'overdue_deliveries' => DeliveryNote::overdue()->count(),
            'by_status' => DeliveryNote::whereBetween('delivery_date', [$startDate, $endDate])
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get()
                ->keyBy('status'),
            'by_deliverer' => DeliveryNote::whereBetween('delivery_date', [$startDate, $endDate])
                ->with('deliveredBy')
                ->get()
                ->groupBy('delivered_by')
                ->map(function ($deliveries) {
                    return [
                        'deliverer' => $deliveries->first()->deliveredBy->name ?? 'Unknown',
                        'count' => $deliveries->count(),
                        'completed' => $deliveries->where('status', 'delivered')->count(),
                        'returned' => $deliveries->where('status', 'returned')->count(),
                    ];
                }),
            'average_delivery_time' => DeliveryNote::delivered()
                ->whereBetween('delivery_date', [$startDate, $endDate])
                ->get()
                ->filter(function($delivery) {
                    return $delivery->delivery_duration !== null;
                })
                ->avg('delivery_duration'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Delivery statistics retrieved successfully'
        ]);
    }

    /**
     * Get deliveries ready for dispatch
     */
    public function readyForDispatch(Request $request)
    {
        $deliveries = DeliveryNote::pending()
            ->with([
                'order.customer', 
                'order.orderItems.product', 
                'preparationSlip'
            ])
            ->whereHas('preparationSlip', function($query) {
                $query->where('status', 'completed');
            })
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $deliveries,
            'message' => 'Deliveries ready for dispatch retrieved successfully'
        ]);
    }
}
