import axios from 'axios';
import { message } from 'antd';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add timestamp to prevent caching
    config.params = {
      ...config.params,
      _t: Date.now(),
    };
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          window.location.href = '/login';
          message.error('Session expirée. Veuillez vous reconnecter.');
          break;
          
        case 403:
          // Forbidden
          message.error('Accès refusé. Vous n\'avez pas les permissions nécessaires.');
          break;
          
        case 404:
          // Not found
          message.error('Ressource non trouvée.');
          break;
          
        case 422:
          // Validation error
          if (data.errors) {
            const errorMessages = Object.values(data.errors).flat();
            errorMessages.forEach(msg => message.error(msg));
          } else {
            message.error(data.message || 'Erreur de validation.');
          }
          break;
          
        case 500:
          // Server error
          message.error('Erreur serveur. Veuillez réessayer plus tard.');
          break;
          
        default:
          // Other errors
          message.error(data.message || 'Une erreur est survenue.');
      }
    } else if (error.request) {
      // Network error
      message.error('Erreur de connexion. Vérifiez votre connexion internet.');
    } else {
      // Other error
      message.error('Une erreur inattendue est survenue.');
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (credentials) => api.post('/login', credentials),
  logout: () => api.post('/logout'),
  me: () => api.get('/me'),
  changePassword: (data) => api.post('/change-password', data),
  refreshToken: () => api.post('/refresh'),
};

export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  createUser: (data) => api.post('/register', data),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),
};

export const receptionAPI = {
  getReceptions: (params) => api.get('/receptions', { params }),
  createReception: (data) => api.post('/receptions', data),
  getReception: (id) => api.get(`/receptions/${id}`),
  updateReception: (id, data) => api.put(`/receptions/${id}`, data),
  deleteReception: (id) => api.delete(`/receptions/${id}`),
  generateLotNumber: () => api.get('/receptions/generate-lot-number'),
  getStatistics: (params) => api.get('/receptions/statistics', { params }),
  downloadPDF: (id) => api.get(`/receptions/${id}/pdf`, { responseType: 'blob' }),
};

export const stockAPI = {
  getStock: (params) => api.get('/stock', { params }),
  getStockItem: (id) => api.get(`/stock/${id}`),
  getLowStock: () => api.get('/stock/low-stock'),
  getMovements: (params) => api.get('/stock/movements', { params }),
  getStockMovements: (id, params) => api.get(`/stock/${id}/movements`, { params }),
  makeAdjustment: (data) => api.post('/stock/adjustment', data),
  updateAlert: (id, data) => api.put(`/stock/${id}/alert`, data),
  getStatistics: () => api.get('/stock/statistics'),
  getValuation: () => api.get('/stock/valuation'),
};

export const ordersAPI = {
  getOrders: (params) => api.get('/orders', { params }),
  createOrder: (data) => api.post('/orders', data),
  getOrder: (id) => api.get(`/orders/${id}`),
  updateOrder: (id, data) => api.put(`/orders/${id}`, data),
  confirmOrder: (id) => api.post(`/orders/${id}/confirm`),
  cancelOrder: (id, data) => api.post(`/orders/${id}/cancel`, data),
  getPreparations: (params) => api.get('/preparations', { params }),
  startPreparation: (id) => api.post(`/orders/${id}/prepare`),
  completePreparation: (id, data) => api.post(`/orders/${id}/complete-preparation`, data),
  getStatistics: (params) => api.get('/orders/statistics', { params }),
  downloadPDF: (id) => api.get(`/orders/${id}/pdf`, { responseType: 'blob' }),
  downloadPreparationPDF: (id) => api.get(`/preparations/${id}/pdf`, { responseType: 'blob' }),
};

export const customersAPI = {
  getCustomers: (params) => api.get('/customers', { params }),
  createCustomer: (data) => api.post('/customers', data),
  updateCustomer: (id, data) => api.put(`/customers/${id}`, data),
  getCreditStatus: (id) => api.get(`/customers/${id}/credit-status`),
  getCustomerOrders: (id, params) => api.get(`/customers/${id}/orders`, { params }),
  getCustomerPayments: (id, params) => api.get(`/customers/${id}/payments`, { params }),
  getCustomersAtRisk: () => api.get('/customers/at-risk'),
};

export const paymentsAPI = {
  getPayments: (params) => api.get('/payments', { params }),
  recordPayment: (data) => api.post('/payments', data),
  getStatistics: (params) => api.get('/payments/statistics', { params }),
};

export const deliveriesAPI = {
  getDeliveries: (params) => api.get('/deliveries', { params }),
  startDelivery: (id) => api.post(`/orders/${id}/deliver`),
  completeDelivery: (id, data) => api.post(`/deliveries/${id}/complete`, data),
  returnDelivery: (id, data) => api.post(`/deliveries/${id}/return`, data),
  getReadyForDispatch: () => api.get('/deliveries/ready-for-dispatch'),
  getStatistics: (params) => api.get('/deliveries/statistics', { params }),
  downloadPDF: (id) => api.get(`/deliveries/${id}/pdf`, { responseType: 'blob' }),
};

export const traceabilityAPI = {
  search: (params) => api.get('/traceability/search', { params }),
  getByLot: (lotNumber) => api.get(`/traceability/lot/${lotNumber}`),
  getByProduct: (id, params) => api.get(`/traceability/product/${id}`, { params }),
  getByCustomer: (id, params) => api.get(`/traceability/customer/${id}`, { params }),
  getByDateRange: (params) => api.get('/traceability/date-range', { params }),
};

export const dashboardAPI = {
  getStats: () => api.get('/dashboard/stats'),
  getDailyStats: (params) => api.get('/dashboard/daily-stats', { params }),
  getMonthlyStats: (params) => api.get('/dashboard/monthly-stats', { params }),
  getTopCustomers: (params) => api.get('/dashboard/top-customers', { params }),
  getLowStockAlerts: () => api.get('/dashboard/low-stock-alerts'),
  getRecentActivities: (params) => api.get('/dashboard/recent-activities', { params }),
};

export const productsAPI = {
  getProducts: () => api.get('/products'),
  createProduct: (data) => api.post('/products', data),
  updateProduct: (id, data) => api.put(`/products/${id}`, data),
};

export const reportsAPI = {
  exportOrders: (params) => api.get('/export/orders', { params, responseType: 'blob' }),
  exportCustomers: (params) => api.get('/export/customers', { params, responseType: 'blob' }),
  exportStock: (params) => api.get('/export/stock', { params, responseType: 'blob' }),
  exportReceptions: (params) => api.get('/export/receptions', { params, responseType: 'blob' }),
};

export default api;
