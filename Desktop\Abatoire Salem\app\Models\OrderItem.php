<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'stock_id',
        'quantity_kg',
        'unit_price',
        'total_price',
    ];

    protected $casts = [
        'quantity_kg' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-calculate total price before saving
        static::saving(function ($orderItem) {
            $orderItem->total_price = $orderItem->quantity_kg * $orderItem->unit_price;
        });

        // Update order total when order item is saved
        static::saved(function ($orderItem) {
            $orderItem->updateOrderTotal();
        });

        // Update order total when order item is deleted
        static::deleted(function ($orderItem) {
            $orderItem->updateOrderTotal();
        });
    }

    /**
     * Update the parent order's total amount
     */
    public function updateOrderTotal()
    {
        $order = $this->order;
        if ($order) {
            $order->total_amount = $order->orderItems()->sum('total_price');
            $order->save();
        }
    }

    /**
     * Find best stock for this order item
     */
    public function findBestStock(): ?Stock
    {
        return Stock::where('product_id', $this->product_id)
            ->available()
            ->whereRaw('(current_weight_kg - reserved_weight_kg) >= ?', [$this->quantity_kg])
            ->orderBy('created_at', 'asc') // FIFO - First In, First Out
            ->first();
    }

    /**
     * Reserve stock for this order item
     */
    public function reserveStock(): bool
    {
        if ($this->stock_id) {
            // Stock already assigned
            return $this->stock->reserve(
                $this->quantity_kg,
                $this->order->user_id,
                "Réservation pour commande {$this->order->order_number}"
            );
        }

        // Find best available stock
        $stock = $this->findBestStock();
        if (!$stock) {
            return false;
        }

        // Reserve the stock
        if ($stock->reserve(
            $this->quantity_kg,
            $this->order->user_id,
            "Réservation pour commande {$this->order->order_number}"
        )) {
            $this->stock_id = $stock->id;
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Unreserve stock for this order item
     */
    public function unreserveStock(): bool
    {
        if (!$this->stock_id) {
            return true;
        }

        return $this->stock->unreserve(
            $this->quantity_kg,
            $this->order->user_id,
            "Annulation réservation commande {$this->order->order_number}"
        );
    }

    /**
     * Consume stock for this order item (when delivered)
     */
    public function consumeStock(): bool
    {
        if (!$this->stock_id) {
            return false;
        }

        return $this->stock->consume(
            $this->quantity_kg,
            $this->order->user_id,
            'order',
            $this->order_id,
            "Livraison commande {$this->order->order_number}"
        );
    }

    /**
     * Relationships
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    /**
     * Scopes
     */
    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByOrder($query, $orderId)
    {
        return $query->where('order_id', $orderId);
    }
}
