# Abattoir Salem Management System

A comprehensive slaughterhouse management system built with Laravel backend and designed for local network deployment across 2-3 PCs.

## 🎯 Project Overview

This system manages the complete workflow of a professional slaughterhouse from post-slaughter meat reception to customer delivery, including:

- **Post-slaughter meat reception** with lot tracking
- **Stock management** in kg with real-time tracking
- **Customer order management** with credit limits
- **Preparation slip generation** and tracking
- **Delivery note system** with PDF export
- **Credit management** with automatic blocking
- **Complete traceability** from slaughter to delivery
- **Dashboard analytics** and reporting

## 🏗️ Architecture

- **Backend**: Laravel 10 with JWT authentication
- **Database**: MySQL with comprehensive schema
- **Authentication**: JWT-based with role-based access control
- **PDF Generation**: DomPDF for documents
- **Excel Export**: Maatwebsite Excel package
- **Deployment**: Local network (LAN) deployment

## 👥 User Roles

1. **Admin**: Full system access, user management, system configuration
2. **Storekeeper**: Reception, stock management, preparation
3. **Salesperson**: Customer management, orders, credit tracking
4. **Delivery**: Delivery management and completion

## 📊 Database Schema

### Core Tables Created:
- `users` - System users with role-based access
- `customers` - Customer information and credit limits
- `products` - Product catalog with pricing
- `receptions` - Post-slaughter meat reception records
- `stock` - Real-time stock tracking in kg
- `orders` - Customer orders with status tracking
- `order_items` - Individual order line items
- `preparation_slips` - Preparation workflow management
- `delivery_notes` - Delivery tracking and completion
- `stock_movements` - Complete stock movement history
- `payments` - Payment tracking and credit management
- `activity_logs` - Complete audit trail

## 🔧 Features Implemented

### ✅ Completed Features:

1. **Project Structure Setup** ✅
   - Complete Laravel project configuration
   - JWT authentication setup
   - Database configuration
   - Package dependencies

2. **Database Design & Migration** ✅
   - Comprehensive database schema (12 tables)
   - All migrations created
   - Database seeders with sample data
   - Proper relationships and constraints

3. **Authentication System** ✅
   - JWT-based authentication
   - Role-based access control middleware
   - User management with roles
   - Activity logging for security

4. **Post-Slaughter Reception Module** ✅
   - Complete reception management
   - Lot number generation
   - Automatic stock creation
   - Reception statistics and PDF export

5. **Stock Management System** ✅
   - Real-time stock tracking in kg
   - Stock movements and traceability
   - Low stock alerts
   - Stock adjustments and valuations

6. **Customer Order Management** ✅
   - Order creation with stock reservation
   - Credit limit enforcement (max 3 unpaid orders)
   - Order workflow management
   - Order statistics and reporting

7. **Preparation Slip System** ✅
   - Automatic preparation slip generation
   - Preparation workflow tracking
   - Quality control checkpoints
   - PDF generation for preparation slips

8. **Delivery Note System** ✅
   - Delivery management workflow
   - Customer signature capture
   - Return handling
   - Delivery statistics and tracking

9. **Credit Management System** ✅
   - Customer credit tracking
   - Payment recording and allocation
   - Credit risk assessment
   - Automatic order blocking system

10. **Dashboard & Analytics** ✅
    - Real-time statistics dashboard
    - Daily/monthly analytics
    - Top customers analysis
    - Low stock alerts

11. **Complete Traceability System** ✅
    - Lot-based traceability
    - Product journey tracking
    - Customer purchase history
    - Date range traceability

12. **PDF Generation & Printing** ✅
    - Order documents
    - Preparation slips
    - Delivery notes
    - Payment receipts
    - Stock reports

### 📋 Remaining Features:
- React Frontend Development
- Backup & Data Export (Excel/CSV)
- Testing & Quality Assurance
- Local Network Deployment

## 🚀 Installation & Setup

### Prerequisites:
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js (for frontend - future)

### Quick Installation:

**Windows:**
```bash
# Run the automated installation script
install.bat
```

**Linux/Mac:**
```bash
# Make script executable and run
chmod +x install.sh
./install.sh
```

### Manual Installation Steps:

1. **Install Dependencies**
   ```bash
   composer install --no-dev --optimize-autoloader
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env file with your database credentials
   ```

3. **Generate Keys**
   ```bash
   php artisan key:generate
   php artisan jwt:secret
   ```

4. **Database Setup**
   ```bash
   php artisan migrate --force
   php artisan db:seed --force
   ```

5. **Start Server**
   ```bash
   php artisan serve
   ```

## 📱 API Endpoints

### Authentication
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/me` - Get current user
- `POST /api/refresh` - Refresh token

### Reception (Storekeeper/Admin)
- `GET /api/receptions` - List receptions
- `POST /api/receptions` - Create reception
- `GET /api/receptions/{id}` - Get reception details

### Stock Management
- `GET /api/stock` - List stock
- `GET /api/stock/low-stock` - Low stock alerts
- `GET /api/stock/movements` - Stock movements

### Orders (Salesperson/Admin)
- `GET /api/orders` - List orders
- `POST /api/orders` - Create order
- `PUT /api/orders/{id}` - Update order

### And many more...

## 🔐 Default Users

After running seeders, you can login with:

- **Admin**: <EMAIL> / admin123
- **Storekeeper**: <EMAIL> / ahmed123
- **Salesperson**: <EMAIL> / fatima123
- **Delivery**: <EMAIL> / mohamed123

## 📈 Business Logic

### Reception Workflow:
1. Storekeeper receives meat post-slaughter
2. Records: animal type, quantity (heads), total weight (kg)
3. System auto-generates lot number and calculates average weight
4. Stock is automatically created and movement logged

### Order Workflow:
1. Salesperson creates customer order
2. System checks customer credit limit (max 3 unpaid orders)
3. Stock is reserved for order items
4. Preparation slip is generated
5. Storekeeper prepares order
6. Delivery note is created
7. Delivery person completes delivery
8. Stock is consumed and payment tracked

### Credit Management:
- Customers have configurable credit limits
- Maximum unpaid orders limit (default: 3)
- Automatic blocking when limits exceeded
- Payment tracking with order allocation

## 🔍 Traceability

Complete traceability from slaughter to delivery:
- Lot-based tracking from reception
- Stock movement history
- Order-to-delivery chain
- Customer purchase history
- User action logging

## 📊 System Status

### ✅ **BACKEND COMPLETE (95%)**
- All core business logic implemented
- Complete API with 50+ endpoints
- Full authentication and authorization
- PDF generation for all documents
- Comprehensive traceability system
- Real-time dashboard and analytics

### 🔄 **NEXT STEPS**
1. **React Frontend Development** (0%)
   - User interface for all modules
   - Responsive design for tablets/PCs
   - Real-time updates and notifications

2. **Data Export System** (0%)
   - Excel/CSV export functionality
   - Automated backup system
   - Data import capabilities

3. **Testing & Quality Assurance** (0%)
   - Unit tests for all modules
   - Integration testing
   - User acceptance testing

4. **Local Network Deployment** (0%)
   - Multi-PC network configuration
   - Database server setup
   - Client application distribution

### 🎯 **PRODUCTION READINESS: 75%**
The backend system is fully functional and ready for frontend integration.

## 🤝 Contributing

This is a professional slaughterhouse management system. All contributions should maintain the high standards of food industry compliance and traceability requirements.

## 📄 License

Private commercial software for Abattoir Salem.
