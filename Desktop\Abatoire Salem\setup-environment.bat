@echo off
echo ========================================
echo    ABATTOIR SALEM COMPLETE SETUP
echo     Installing All Prerequisites
echo ========================================
echo.

REM Create temp directory for downloads
if not exist "%TEMP%\abattoir-setup" mkdir "%TEMP%\abattoir-setup"
cd "%TEMP%\abattoir-setup"

echo [1/6] Downloading PHP 8.2...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://windows.php.net/downloads/releases/php-8.2.13-Win32-vs16-x64.zip' -OutFile 'php.zip'}"

echo [2/6] Extracting PHP...
powershell -Command "Expand-Archive -Path 'php.zip' -DestinationPath 'C:\php' -Force"

echo [3/6] Configuring PHP...
copy "C:\php\php.ini-development" "C:\php\php.ini"
powershell -Command "(Get-Content 'C:\php\php.ini') -replace ';extension=pdo_sqlite', 'extension=pdo_sqlite' | Set-Content 'C:\php\php.ini'"
powershell -Command "(Get-Content 'C:\php\php.ini') -replace ';extension=openssl', 'extension=openssl' | Set-Content 'C:\php\php.ini'"
powershell -Command "(Get-Content 'C:\php\php.ini') -replace ';extension=mbstring', 'extension=mbstring' | Set-Content 'C:\php\php.ini'"

echo [4/6] Adding PHP to PATH...
setx PATH "%PATH%;C:\php" /M

echo [5/6] Downloading Composer...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://getcomposer.org/installer' -OutFile 'composer-setup.php'}"
C:\php\php.exe composer-setup.php --install-dir=C:\php --filename=composer

echo [6/6] Downloading Node.js...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.18.2/node-v18.18.2-x64.msi' -OutFile 'nodejs.msi'}"

echo Installing Node.js (this will open an installer)...
start /wait msiexec /i nodejs.msi /quiet

echo.
echo ========================================
echo     ENVIRONMENT SETUP COMPLETE!
echo ========================================
echo.
echo Please restart your command prompt and run:
echo   php --version
echo   composer --version  
echo   node --version
echo.
echo Then run: install.bat
echo.
pause
