import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { BarChartOutlined, ToolOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Reports = () => {
  return (
    <div>
      <Title level={2}>
        <BarChartOutlined /> Rapports et Analyses
      </Title>
      
      <Card>
        <Result
          icon={<ToolOutlined />}
          title="Module en Développement"
          subTitle="Le module de rapports est en cours de développement. Il comprendra tous les rapports et analyses nécessaires."
          extra={
            <div>
              <p><strong>Fonctionnalités prévues :</strong></p>
              <ul style={{ textAlign: 'left', display: 'inline-block' }}>
                <li>Rapports de ventes par période</li>
                <li>Analyse des stocks et rotations</li>
                <li>Performance des clients</li>
                <li>Statistiques de réception</li>
                <li>Rapports de trésorerie</li>
                <li>Export Excel/PDF des données</li>
              </ul>
              <Button type="primary" onClick={() => window.history.back()}>
                Retour
              </Button>
            </div>
          }
        />
      </Card>
    </div>
  );
};

export default Reports;
