<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define gates for role-based access
        Gate::define('admin-access', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('storekeeper-access', function ($user) {
            return in_array($user->role, ['admin', 'storekeeper']);
        });

        Gate::define('salesperson-access', function ($user) {
            return in_array($user->role, ['admin', 'salesperson']);
        });

        Gate::define('delivery-access', function ($user) {
            return in_array($user->role, ['admin', 'delivery']);
        });

        // Specific permissions
        Gate::define('manage-users', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-receptions', function ($user) {
            return in_array($user->role, ['admin', 'storekeeper']);
        });

        Gate::define('manage-stock', function ($user) {
            return in_array($user->role, ['admin', 'storekeeper']);
        });

        Gate::define('manage-orders', function ($user) {
            return in_array($user->role, ['admin', 'salesperson']);
        });

        Gate::define('manage-deliveries', function ($user) {
            return in_array($user->role, ['admin', 'delivery']);
        });

        Gate::define('manage-credit', function ($user) {
            return in_array($user->role, ['admin', 'salesperson']);
        });

        Gate::define('view-reports', function ($user) {
            return in_array($user->role, ['admin', 'salesperson']);
        });
    }
}
