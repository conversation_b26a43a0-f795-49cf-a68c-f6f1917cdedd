@extends('pdf.layout')

@section('title', 'Bon de Préparation')

@section('document-title')
    Bon de Préparation N° {{ $preparation->slip_number }}
@endsection

@section('content')
    <div class="document-info">
        <div class="left">
            <div class="info-box">
                <div class="info-title">Commande à Préparer</div>
                <div class="info-content">
                    <strong>N° Commande:</strong> {{ $order->order_number }}<br>
                    <strong>Date commande:</strong> {{ $order->order_date->format('d/m/Y') }}<br>
                    <strong>Client:</strong> {{ $order->customer->name }}<br>
                    @if($order->delivery_date)
                        <strong>Date livraison prévue:</strong> {{ $order->delivery_date->format('d/m/Y') }}
                    @endif
                </div>
            </div>
        </div>
        
        <div class="right">
            <div class="info-box">
                <div class="info-title">Statut de Préparation</div>
                <div class="info-content">
                    <strong>N° Préparation:</strong> {{ $preparation->slip_number }}<br>
                    <strong>Statut:</strong> 
                    <span class="status-badge status-{{ $preparation->status }}">{{ ucfirst($preparation->status) }}</span><br>
                    <strong>Créé le:</strong> {{ $preparation->created_date->format('d/m/Y à H:i') }}<br>
                    @if($preparation->started_at)
                        <strong>Démarré le:</strong> {{ $preparation->started_at->format('d/m/Y à H:i') }}<br>
                    @endif
                    @if($preparation->completed_at)
                        <strong>Terminé le:</strong> {{ $preparation->completed_at->format('d/m/Y à H:i') }}<br>
                    @endif
                    @if($preparation->preparedBy)
                        <strong>Préparé par:</strong> {{ $preparation->preparedBy->name }}
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if($preparation->preparation_duration)
        <div class="highlight">
            <strong>Durée de préparation:</strong> {{ $preparation->preparation_duration }} minutes
        </div>
    @endif

    <table class="table">
        <thead>
            <tr>
                <th style="width: 5%">#</th>
                <th style="width: 30%">Produit</th>
                <th style="width: 15%">Type Animal</th>
                <th style="width: 15%" class="center">Quantité (kg)</th>
                <th style="width: 20%">Emplacement Stock</th>
                <th style="width: 15%" class="center">Préparé ✓</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->orderItems as $index => $item)
                <tr>
                    <td class="center">{{ $index + 1 }}</td>
                    <td>{{ $item->product->name }}</td>
                    <td>{{ $item->product->animal_type }}</td>
                    <td class="number">{{ number_format($item->quantity_kg, 3) }}</td>
                    <td>
                        @if($item->stock)
                            Lot: {{ $item->stock->reception->lot_number }}<br>
                            <small>Reçu le: {{ $item->stock->reception->reception_date->format('d/m/Y') }}</small>
                        @else
                            <em>Stock non assigné</em>
                        @endif
                    </td>
                    <td class="center">
                        @if($preparation->status === 'completed')
                            ✓
                        @else
                            ☐
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="document-info mt-20">
        <div class="left">
            <div class="info-box">
                <div class="info-title">Instructions de Préparation</div>
                <div class="info-content">
                    • Vérifier la qualité de chaque produit<br>
                    • Respecter la chaîne du froid<br>
                    • Emballer selon les normes d'hygiène<br>
                    • Étiqueter avec le lot d'origine<br>
                    • Peser et vérifier les quantités<br>
                    • Signer après vérification complète
                </div>
            </div>
        </div>
        
        <div class="right">
            <div class="info-box">
                <div class="info-title">Contrôle Qualité</div>
                <div class="info-content">
                    <strong>Température vérifiée:</strong> ☐<br>
                    <strong>Aspect visuel OK:</strong> ☐<br>
                    <strong>Odeur normale:</strong> ☐<br>
                    <strong>Emballage conforme:</strong> ☐<br>
                    <strong>Étiquetage complet:</strong> ☐<br>
                    <strong>Poids vérifié:</strong> ☐
                </div>
            </div>
        </div>
    </div>

    @if($preparation->notes)
        <div class="highlight">
            <strong>Notes de préparation:</strong> {{ $preparation->notes }}
        </div>
    @endif

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-title">Magasinier</div>
            @if($preparation->preparedBy)
                <div class="signature-line">{{ $preparation->preparedBy->name }}</div>
            @else
                <div class="signature-line">Signature</div>
            @endif
        </div>
        
        <div class="signature-box">
            <div class="signature-title">Contrôle Qualité</div>
            <div class="signature-line">Signature et date</div>
        </div>
        
        <div class="signature-box">
            <div class="signature-title">Responsable</div>
            <div class="signature-line">Signature et cachet</div>
        </div>
    </div>

    @if($preparation->status === 'pending')
        <div class="highlight mt-20">
            <strong>À faire:</strong> Cette préparation est en attente. 
            Commencer la préparation dès que possible selon l'ordre de priorité.
        </div>
    @elseif($preparation->status === 'in_progress')
        <div class="highlight mt-20">
            <strong>En cours:</strong> Préparation démarrée le {{ $preparation->started_at->format('d/m/Y à H:i') }}. 
            Terminer dans les plus brefs délais.
        </div>
    @elseif($preparation->status === 'completed')
        <div class="highlight mt-20">
            <strong>Terminé:</strong> Préparation terminée le {{ $preparation->completed_at->format('d/m/Y à H:i') }}. 
            Prêt pour livraison.
        </div>
    @endif

    <div class="mt-20 font-small text-center">
        <strong>Procédures de sécurité:</strong><br>
        - Port obligatoire des équipements de protection individuelle<br>
        - Respect des règles d'hygiène et de sécurité alimentaire<br>
        - Signaler immédiatement tout problème de qualité<br>
        - Maintenir la traçabilité des lots
    </div>
@endsection
