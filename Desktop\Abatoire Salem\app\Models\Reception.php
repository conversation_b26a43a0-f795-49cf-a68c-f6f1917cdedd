<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Reception extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'user_id',
        'lot_number',
        'quantity_heads',
        'total_weight_kg',
        'average_weight_kg',
        'origin',
        'slaughter_date',
        'reception_date',
        'notes',
    ];

    protected $casts = [
        'total_weight_kg' => 'decimal:3',
        'average_weight_kg' => 'decimal:3',
        'slaughter_date' => 'date',
        'reception_date' => 'datetime',
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-calculate average weight before saving
        static::saving(function ($reception) {
            if ($reception->quantity_heads > 0) {
                $reception->average_weight_kg = $reception->total_weight_kg / $reception->quantity_heads;
            }
        });

        // Create stock entry after reception is created
        static::created(function ($reception) {
            $reception->createStockEntry();
        });
    }

    /**
     * Generate unique lot number
     */
    public static function generateLotNumber(): string
    {
        $date = Carbon::now()->format('Ymd');
        $count = static::whereDate('created_at', Carbon::today())->count() + 1;
        return "LOT{$date}" . str_pad($count, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Create stock entry for this reception
     */
    public function createStockEntry()
    {
        Stock::create([
            'product_id' => $this->product_id,
            'reception_id' => $this->id,
            'current_weight_kg' => $this->total_weight_kg,
            'initial_weight_kg' => $this->total_weight_kg,
            'reserved_weight_kg' => 0,
            'minimum_alert_kg' => 10, // Default alert threshold
            'is_available' => true,
        ]);

        // Log stock movement
        StockMovement::create([
            'stock_id' => Stock::where('reception_id', $this->id)->first()->id,
            'user_id' => $this->user_id,
            'movement_type' => 'in',
            'quantity_kg' => $this->total_weight_kg,
            'balance_after' => $this->total_weight_kg,
            'reference_type' => 'reception',
            'reference_id' => $this->id,
            'reason' => "Réception lot {$this->lot_number}",
            'movement_date' => $this->reception_date,
        ]);
    }

    /**
     * Relationships
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function stock()
    {
        return $this->hasOne(Stock::class);
    }

    /**
     * Scopes
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('reception_date', [$startDate, $endDate]);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByLot($query, $lotNumber)
    {
        return $query->where('lot_number', 'like', "%{$lotNumber}%");
    }

    public function scopeByOrigin($query, $origin)
    {
        return $query->where('origin', 'like', "%{$origin}%");
    }
}
