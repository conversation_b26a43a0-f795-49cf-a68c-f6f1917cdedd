import React from 'react';
import { Card, List, Badge, Typography, Button, Empty, Spin, Tag } from 'antd';
import { 
  WarningOutlined, 
  ExclamationCircleOutlined,
  DatabaseOutlined,
  EyeOutlined 
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { dashboardAPI } from '../../../services/api';
import dayjs from 'dayjs';

const { Text, Title } = Typography;

const LowStockAlerts = () => {
  const navigate = useNavigate();

  const { data, isLoading } = useQuery(
    'lowStockAlerts',
    dashboardAPI.getLowStockAlerts,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const alerts = data?.data?.data || [];

  const getUrgencyConfig = (urgencyLevel) => {
    switch (urgencyLevel) {
      case 1:
        return {
          color: '#ff4d4f',
          text: 'CRITIQUE',
          icon: <ExclamationCircleOutlined />,
          badge: 'error',
        };
      case 2:
        return {
          color: '#fa8c16',
          text: 'URGENT',
          icon: <WarningOutlined />,
          badge: 'warning',
        };
      case 3:
        return {
          color: '#faad14',
          text: 'ATTENTION',
          icon: <WarningOutlined />,
          badge: 'processing',
        };
      default:
        return {
          color: '#52c41a',
          text: 'NORMAL',
          icon: <DatabaseOutlined />,
          badge: 'success',
        };
    }
  };

  const handleViewStock = (stockId) => {
    navigate(`/stock?highlight=${stockId}`);
  };

  const handleViewAllAlerts = () => {
    navigate('/stock?filter=low_stock');
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <WarningOutlined style={{ color: '#fa8c16' }} />
          <span>Alertes Stock</span>
          {alerts.length > 0 && (
            <Badge count={alerts.length} style={{ backgroundColor: '#fa8c16' }} />
          )}
        </div>
      }
      className="chart-container"
      extra={
        alerts.length > 0 && (
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={handleViewAllAlerts}
          >
            Voir tout
          </Button>
        )
      }
    >
      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : alerts.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Aucune alerte stock"
          style={{ padding: '20px' }}
        />
      ) : (
        <>
          <div style={{ marginBottom: '16px' }}>
            <Text type="secondary">
              {alerts.length} produit{alerts.length > 1 ? 's' : ''} en stock faible
            </Text>
          </div>
          
          <List
            itemLayout="horizontal"
            dataSource={alerts.slice(0, 5)} // Show only first 5 alerts
            renderItem={(alert) => {
              const urgencyConfig = getUrgencyConfig(alert.urgency_level);
              
              return (
                <List.Item
                  style={{
                    padding: '12px 0',
                    borderLeft: `3px solid ${urgencyConfig.color}`,
                    paddingLeft: '12px',
                    marginLeft: '-12px',
                    marginBottom: '8px',
                    backgroundColor: alert.urgency_level <= 2 ? '#fff2f0' : 'transparent',
                  }}
                  actions={[
                    <Button
                      type="link"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewStock(alert.id)}
                    >
                      Voir
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge status={urgencyConfig.badge}>
                        {urgencyConfig.icon}
                      </Badge>
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Text strong>{alert.product?.name}</Text>
                        <Tag color={urgencyConfig.color} size="small">
                          {urgencyConfig.text}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '4px' }}>
                          <Text type="secondary">
                            Stock disponible: <Text strong style={{ color: urgencyConfig.color }}>
                              {alert.available_weight_kg} kg
                            </Text>
                          </Text>
                        </div>
                        <div style={{ marginBottom: '4px' }}>
                          <Text type="secondary">
                            Seuil d'alerte: {alert.minimum_alert_kg} kg
                          </Text>
                        </div>
                        <div style={{ display: 'flex', gap: '12px', fontSize: '12px' }}>
                          <Text type="secondary">
                            Lot: {alert.lot_number}
                          </Text>
                          <Text type="secondary">
                            Reçu: {dayjs(alert.reception_date).format('DD/MM/YYYY')}
                          </Text>
                          <Text type="secondary">
                            {alert.days_in_stock} jour{alert.days_in_stock > 1 ? 's' : ''}
                          </Text>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
          
          {alerts.length > 5 && (
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Button type="dashed" onClick={handleViewAllAlerts}>
                Voir {alerts.length - 5} autre{alerts.length - 5 > 1 ? 's' : ''} alerte{alerts.length - 5 > 1 ? 's' : ''}
              </Button>
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default LowStockAlerts;
