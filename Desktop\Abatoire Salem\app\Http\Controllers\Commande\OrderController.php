<?php

namespace App\Http\Controllers\Commande;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use App\Models\Product;
use App\Models\PreparationSlip;
use App\Models\ActivityLog;
use App\Services\PDFService;
use Carbon\Carbon;

class OrderController extends Controller
{
    /**
     * Display a listing of orders
     */
    public function index(Request $request)
    {
        $query = Order::with(['customer', 'user', 'orderItems.product']);

        // Apply filters
        if ($request->has('customer_id') && $request->customer_id) {
            $query->byCustomer($request->customer_id);
        }

        if ($request->has('status') && $request->status) {
            $query->byStatus($request->status);
        }

        if ($request->has('payment_status') && $request->payment_status) {
            $query->byPaymentStatus($request->payment_status);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        if ($request->has('today') && $request->boolean('today')) {
            $query->today();
        }

        if ($request->has('unpaid') && $request->boolean('unpaid')) {
            $query->unpaid();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'order_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $orders = $query->paginate($perPage);

        // Add calculated fields
        $orders->getCollection()->transform(function ($order) {
            $order->remaining_amount = $order->remaining_amount;
            $order->can_be_cancelled = $order->canBeCancelled();
            $order->can_be_prepared = $order->canBePrepared();
            return $order;
        });

        return response()->json([
            'success' => true,
            'data' => $orders,
            'message' => 'Orders retrieved successfully'
        ]);
    }

    /**
     * Store a newly created order
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'payment_method' => 'required|in:cash,credit,bank_transfer',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date|after:order_date',
            'notes' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity_kg' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check customer eligibility
        $customer = Customer::find($request->customer_id);
        if (!$customer->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Customer account is inactive'
            ], 400);
        }

        if (!$customer->canPlaceOrder()) {
            return response()->json([
                'success' => false,
                'message' => "Customer has reached maximum unpaid orders limit ({$customer->max_unpaid_orders})"
            ], 400);
        }

        // Check product availability and calculate total
        $totalAmount = 0;
        $stockChecks = [];

        foreach ($request->items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => "Product {$product->name} is not active"
                ], 400);
            }

            // Check stock availability
            if ($product->available_stock < $item['quantity_kg']) {
                return response()->json([
                    'success' => false,
                    'message' => "Insufficient stock for {$product->name}. Available: {$product->available_stock}kg, Requested: {$item['quantity_kg']}kg"
                ], 400);
            }

            $itemTotal = $item['quantity_kg'] * $item['unit_price'];
            $totalAmount += $itemTotal;
            
            $stockChecks[] = [
                'product' => $product,
                'quantity' => $item['quantity_kg'],
                'unit_price' => $item['unit_price'],
                'total' => $itemTotal
            ];
        }

        DB::beginTransaction();
        try {
            // Create order
            $order = Order::create([
                'customer_id' => $request->customer_id,
                'user_id' => auth()->id(),
                'status' => 'pending',
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'order_date' => $request->order_date,
                'delivery_date' => $request->delivery_date,
                'notes' => $request->notes,
            ]);

            // Create order items
            foreach ($request->items as $index => $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['product_id'],
                    'quantity_kg' => $item['quantity_kg'],
                    'unit_price' => $item['unit_price'],
                ]);
            }

            // Reserve stock for all items
            if (!$order->reserveStock()) {
                throw new \Exception('Failed to reserve stock for order');
            }

            DB::commit();

            // Log activity
            ActivityLog::logActivity(
                'created',
                "Created order {$order->order_number} for customer {$customer->name}",
                'Order',
                $order->id,
                null,
                $order->toArray()
            );

            // Load relationships for response
            $order->load(['customer', 'user', 'orderItems.product']);
            $order->remaining_amount = $order->remaining_amount;

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified order
     */
    public function show(Order $order)
    {
        $order->load([
            'customer', 
            'user', 
            'orderItems.product',
            'orderItems.stock.reception',
            'preparationSlip.preparedBy',
            'deliveryNote.deliveredBy',
            'payments.user'
        ]);

        $order->remaining_amount = $order->remaining_amount;
        $order->can_be_cancelled = $order->canBeCancelled();
        $order->can_be_prepared = $order->canBePrepared();
        $order->can_be_delivered = $order->canBeDelivered();

        return response()->json([
            'success' => true,
            'data' => $order,
            'message' => 'Order retrieved successfully'
        ]);
    }

    /**
     * Update the specified order
     */
    public function update(Request $request, Order $order)
    {
        if (!$order->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'Order cannot be modified in current status'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'delivery_date' => 'nullable|date|after:order_date',
            'notes' => 'nullable|string|max:1000',
            'items' => 'sometimes|array|min:1',
            'items.*.product_id' => 'required_with:items|exists:products,id',
            'items.*.quantity_kg' => 'required_with:items|numeric|min:0.001',
            'items.*.unit_price' => 'required_with:items|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $oldValues = $order->toArray();

            // If items are being updated
            if ($request->has('items')) {
                // Unreserve current stock
                $order->unreserveStock();

                // Delete existing items
                $order->orderItems()->delete();

                // Create new items
                $totalAmount = 0;
                foreach ($request->items as $item) {
                    $product = Product::find($item['product_id']);
                    if ($product->available_stock < $item['quantity_kg']) {
                        throw new \Exception("Insufficient stock for {$product->name}");
                    }

                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $item['product_id'],
                        'quantity_kg' => $item['quantity_kg'],
                        'unit_price' => $item['unit_price'],
                    ]);

                    $totalAmount += $item['quantity_kg'] * $item['unit_price'];
                }

                $order->total_amount = $totalAmount;

                // Reserve new stock
                if (!$order->reserveStock()) {
                    throw new \Exception('Failed to reserve stock for updated order');
                }
            }

            // Update other fields
            $order->update($request->only(['delivery_date', 'notes']));

            DB::commit();

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Updated order {$order->order_number}",
                'Order',
                $order->id,
                $oldValues,
                $order->fresh()->toArray()
            );

            $order->load(['customer', 'user', 'orderItems.product']);
            $order->remaining_amount = $order->remaining_amount;

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm order
     */
    public function confirm(Order $order)
    {
        if ($order->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Only pending orders can be confirmed'
            ], 400);
        }

        try {
            $order->update(['status' => 'confirmed']);

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Confirmed order {$order->order_number}",
                'Order',
                $order->id,
                ['status' => 'pending'],
                ['status' => 'confirmed']
            );

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order confirmed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel order
     */
    public function cancel(Request $request, Order $order)
    {
        if (!$order->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'Order cannot be cancelled in current status'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Unreserve stock
            $order->unreserveStock();

            $order->update([
                'status' => 'cancelled',
                'notes' => ($order->notes ? $order->notes . "\n\n" : '') . "Cancelled: " . $request->reason
            ]);

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Cancelled order {$order->order_number}: {$request->reason}",
                'Order',
                $order->id,
                ['status' => $order->getOriginal('status')],
                ['status' => 'cancelled']
            );

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get preparation slips
     */
    public function preparations(Request $request)
    {
        $query = PreparationSlip::with(['order.customer', 'order.orderItems.product', 'preparedBy']);

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->byStatus($request->status);
        }

        if ($request->has('prepared_by') && $request->prepared_by) {
            $query->byPreparer($request->prepared_by);
        }

        if ($request->has('today') && $request->boolean('today')) {
            $query->today();
        }

        if ($request->has('overdue') && $request->boolean('overdue')) {
            $query->overdue();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $preparations = $query->paginate($perPage);

        // Add calculated fields
        $preparations->getCollection()->transform(function ($prep) {
            $prep->is_overdue = $prep->isOverdue();
            $prep->preparation_duration = $prep->preparation_duration;
            return $prep;
        });

        return response()->json([
            'success' => true,
            'data' => $preparations,
            'message' => 'Preparation slips retrieved successfully'
        ]);
    }

    /**
     * Start preparation
     */
    public function startPreparation(Order $order)
    {
        if (!$order->canBePrepared()) {
            return response()->json([
                'success' => false,
                'message' => 'Order cannot be prepared in current status'
            ], 400);
        }

        try {
            // Create preparation slip if it doesn't exist
            $preparationSlip = $order->preparationSlip;
            if (!$preparationSlip) {
                $preparationSlip = PreparationSlip::create([
                    'order_id' => $order->id,
                    'status' => 'pending',
                    'created_date' => now(),
                ]);
            }

            // Start preparation
            if (!$preparationSlip->startPreparation(auth()->id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to start preparation'
                ], 400);
            }

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Started preparation for order {$order->order_number}",
                'PreparationSlip',
                $preparationSlip->id,
                ['status' => 'pending'],
                ['status' => 'in_progress']
            );

            $preparationSlip->load(['order.customer', 'order.orderItems.product', 'preparedBy']);

            return response()->json([
                'success' => true,
                'data' => $preparationSlip,
                'message' => 'Preparation started successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start preparation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete preparation
     */
    public function completePreparation(Request $request, Order $order)
    {
        $preparationSlip = $order->preparationSlip;
        if (!$preparationSlip) {
            return response()->json([
                'success' => false,
                'message' => 'No preparation slip found for this order'
            ], 404);
        }

        if ($preparationSlip->status !== 'in_progress') {
            return response()->json([
                'success' => false,
                'message' => 'Preparation is not in progress'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Complete preparation
            if (!$preparationSlip->completePreparation()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to complete preparation'
                ], 400);
            }

            // Update notes if provided
            if ($request->has('notes')) {
                $preparationSlip->update(['notes' => $request->notes]);
            }

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Completed preparation for order {$order->order_number}",
                'PreparationSlip',
                $preparationSlip->id,
                ['status' => 'in_progress'],
                ['status' => 'completed']
            );

            $preparationSlip->load(['order.customer', 'order.orderItems.product', 'preparedBy']);
            $preparationSlip->preparation_duration = $preparationSlip->preparation_duration;

            return response()->json([
                'success' => true,
                'data' => $preparationSlip,
                'message' => 'Preparation completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete preparation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order statistics
     */
    public function statistics(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $stats = [
            'total_orders' => Order::byDateRange($startDate, $endDate)->count(),
            'total_amount' => Order::byDateRange($startDate, $endDate)->sum('total_amount'),
            'paid_amount' => Order::byDateRange($startDate, $endDate)->sum('paid_amount'),
            'by_status' => Order::byDateRange($startDate, $endDate)
                ->selectRaw('status, COUNT(*) as count, SUM(total_amount) as total_amount')
                ->groupBy('status')
                ->get()
                ->keyBy('status'),
            'by_payment_status' => Order::byDateRange($startDate, $endDate)
                ->selectRaw('payment_status, COUNT(*) as count, SUM(total_amount) as total_amount')
                ->groupBy('payment_status')
                ->get()
                ->keyBy('payment_status'),
            'top_customers' => Order::byDateRange($startDate, $endDate)
                ->with('customer')
                ->selectRaw('customer_id, COUNT(*) as order_count, SUM(total_amount) as total_amount')
                ->groupBy('customer_id')
                ->orderBy('total_amount', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Order statistics retrieved successfully'
        ]);
    }

    /**
     * Generate order PDF
     */
    public function generatePDF(Order $order, PDFService $pdfService)
    {
        try {
            $pdf = $pdfService->generateOrderPDF($order);

            return $pdf->download("commande_{$order->order_number}.pdf");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate preparation slip PDF
     */
    public function generatePreparationPDF(PreparationSlip $preparation, PDFService $pdfService)
    {
        try {
            $pdf = $pdfService->generatePreparationPDF($preparation);

            return $pdf->download("preparation_{$preparation->slip_number}.pdf");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ], 500);
        }
    }
}
