import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Alert,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  LoginOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login, isAuthenticated } = useAuth();
  const location = useLocation();

  // Redirect if already authenticated
  if (isAuthenticated) {
    const from = location.state?.from?.pathname || '/dashboard';
    return <Navigate to={from} replace />;
  }

  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');

    try {
      const result = await login(values);
      if (!result.success) {
        setError(result.error || 'Erreur de connexion');
      }
    } catch (err) {
      setError('Une erreur inattendue est survenue');
    } finally {
      setLoading(false);
    }
  };

  const defaultUsers = [
    {
      role: 'Administrateur',
      email: '<EMAIL>',
      password: 'admin123',
      color: '#e74c3c',
    },
    {
      role: 'Magasinier',
      email: '<EMAIL>',
      password: 'ahmed123',
      color: '#3498db',
    },
    {
      role: 'Commercial',
      email: '<EMAIL>',
      password: 'fatima123',
      color: '#27ae60',
    },
    {
      role: 'Livreur',
      email: '<EMAIL>',
      password: 'mohamed123',
      color: '#f39c12',
    },
  ];

  const fillCredentials = (email, password) => {
    form.setFieldsValue({ email, password });
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
    >
      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: '1200px' }}>
        {/* Login Form */}
        <Col xs={24} lg={12}>
          <Card
            style={{
              maxWidth: '400px',
              margin: '0 auto',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
              borderRadius: '12px',
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <Title level={2} style={{ color: '#2c3e50', marginBottom: '8px' }}>
                Abattoir Salem
              </Title>
              <Text type="secondary">
                Système de Gestion Intégré
              </Text>
            </div>

            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{ marginBottom: '24px' }}
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Veuillez saisir votre email' },
                  { type: 'email', message: 'Format email invalide' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="<EMAIL>"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="Mot de passe"
                rules={[
                  { required: true, message: 'Veuillez saisir votre mot de passe' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Votre mot de passe"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '500',
                  }}
                >
                  Se connecter
                </Button>
              </Form.Item>
            </Form>

            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                © 2024 Abattoir Salem. Tous droits réservés.
              </Text>
            </div>
          </Card>
        </Col>

        {/* Demo Credentials */}
        <Col xs={24} lg={12}>
          <Card
            title="Comptes de démonstration"
            style={{
              maxWidth: '500px',
              margin: '0 auto',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
              borderRadius: '12px',
            }}
            headStyle={{
              backgroundColor: '#f8f9fa',
              borderRadius: '12px 12px 0 0',
            }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Text type="secondary">
                Cliquez sur un compte pour remplir automatiquement les champs :
              </Text>

              {defaultUsers.map((user, index) => (
                <Card
                  key={index}
                  size="small"
                  hoverable
                  onClick={() => fillCredentials(user.email, user.password)}
                  style={{
                    borderLeft: `4px solid ${user.color}`,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  bodyStyle={{ padding: '12px 16px' }}
                >
                  <Row justify="space-between" align="middle">
                    <Col>
                      <div>
                        <Text strong style={{ color: user.color }}>
                          {user.role}
                        </Text>
                      </div>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {user.email}
                        </Text>
                      </div>
                    </Col>
                    <Col>
                      <Button type="link" size="small">
                        Utiliser
                      </Button>
                    </Col>
                  </Row>
                </Card>
              ))}

              <Divider />

              <div>
                <Title level={5} style={{ marginBottom: '12px' }}>
                  Fonctionnalités par rôle :
                </Title>
                <ul style={{ fontSize: '13px', color: '#666', paddingLeft: '20px' }}>
                  <li><strong>Administrateur :</strong> Accès complet au système</li>
                  <li><strong>Magasinier :</strong> Réception, stock, préparation</li>
                  <li><strong>Commercial :</strong> Commandes, clients, paiements</li>
                  <li><strong>Livreur :</strong> Livraisons et retours</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
