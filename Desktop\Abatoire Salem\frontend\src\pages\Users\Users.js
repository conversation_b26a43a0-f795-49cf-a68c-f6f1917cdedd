import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { TeamOutlined, ToolOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Users = () => {
  return (
    <div>
      <Title level={2}>
        <TeamOutlined /> Gestion des Utilisateurs
      </Title>
      
      <Card>
        <Result
          icon={<ToolOutlined />}
          title="Module en Développement"
          subTitle="Le module de gestion des utilisateurs est en cours de développement. Il comprendra la gestion complète des comptes utilisateurs."
          extra={
            <div>
              <p><strong>Fonctionnalités prévues :</strong></p>
              <ul style={{ textAlign: 'left', display: 'inline-block' }}>
                <li>Création et modification des comptes utilisateurs</li>
                <li>Gestion des rôles et permissions</li>
                <li>Historique des connexions</li>
                <li>Réinitialisation des mots de passe</li>
                <li>Audit des actions utilisateurs</li>
                <li>Configuration des accès par module</li>
              </ul>
              <Button type="primary" onClick={() => window.history.back()}>
                Retour
              </Button>
            </div>
          }
        />
      </Card>
    </div>
  );
};

export default Users;
