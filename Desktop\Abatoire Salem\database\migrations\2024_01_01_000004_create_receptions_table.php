<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who received it
            $table->string('lot_number')->unique(); // e.g., "LOT20250726A"
            $table->integer('quantity_heads'); // Number of animals (e.g., 200)
            $table->decimal('total_weight_kg', 10, 3); // Total weight in kg (e.g., 600.000)
            $table->decimal('average_weight_kg', 8, 3); // Calculated automatically
            $table->string('origin')->nullable(); // e.g., "<PERSON><PERSON><PERSON>"
            $table->date('slaughter_date');
            $table->datetime('reception_date');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receptions');
    }
};
