import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Progress,
  Alert,
} from 'antd';
import {
  DatabaseOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { stockAPI, productsAPI } from '../../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const Stock = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showLowStock, setShowLowStock] = useState(false);
  const [adjustmentModalVisible, setAdjustmentModalVisible] = useState(false);
  const [selectedStock, setSelectedStock] = useState(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // Fetch stock
  const { data: stock, isLoading } = useQuery(
    ['stock', { searchText, selectedProduct, showLowStock }],
    () => stockAPI.getStock({
      search: searchText,
      product_id: selectedProduct,
      low_stock: showLowStock,
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch products for filter
  const { data: products } = useQuery('products', productsAPI.getProducts);

  // Fetch statistics
  const { data: stats } = useQuery('stockStats', stockAPI.getStatistics);

  // Fetch low stock alerts
  const { data: lowStockAlerts } = useQuery('lowStockAlerts', stockAPI.getLowStock);

  // Stock adjustment mutation
  const adjustmentMutation = useMutation(stockAPI.makeAdjustment, {
    onSuccess: () => {
      message.success('Ajustement de stock effectué avec succès');
      setAdjustmentModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('stock');
      queryClient.invalidateQueries('stockStats');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de l\'ajustement');
    },
  });

  const getStockStatus = (stock) => {
    const availableWeight = stock.current_weight_kg - stock.reserved_weight_kg;
    const alertThreshold = stock.minimum_alert_kg;

    if (availableWeight <= 0) {
      return { status: 'error', text: 'RUPTURE', color: '#ff4d4f' };
    } else if (availableWeight <= alertThreshold * 0.5) {
      return { status: 'error', text: 'CRITIQUE', color: '#ff4d4f' };
    } else if (availableWeight <= alertThreshold) {
      return { status: 'warning', text: 'FAIBLE', color: '#faad14' };
    } else {
      return { status: 'success', text: 'NORMAL', color: '#52c41a' };
    }
  };

  const columns = [
    {
      title: 'Produit',
      dataIndex: ['product', 'name'],
      key: 'product',
    },
    {
      title: 'N° Lot',
      dataIndex: ['reception', 'lot_number'],
      key: 'lot_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Stock Initial',
      dataIndex: 'initial_weight_kg',
      key: 'initial_weight_kg',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Stock Actuel',
      dataIndex: 'current_weight_kg',
      key: 'current_weight_kg',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Réservé',
      dataIndex: 'reserved_weight_kg',
      key: 'reserved_weight_kg',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Disponible',
      key: 'available_weight',
      align: 'right',
      render: (_, record) => {
        const available = record.current_weight_kg - record.reserved_weight_kg;
        return `${available.toFixed(3)} kg`;
      },
    },
    {
      title: 'Statut',
      key: 'status',
      render: (_, record) => {
        const status = getStockStatus(record);
        return (
          <Tag color={status.color}>
            {status.text}
          </Tag>
        );
      },
    },
    {
      title: 'Date Réception',
      dataIndex: ['reception', 'reception_date'],
      key: 'reception_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleAdjustment(record)}
          />
        </Space>
      ),
    },
  ];

  const handleViewDetails = (stock) => {
    // Navigate to stock details page
    console.log('View details for stock:', stock.id);
  };

  const handleAdjustment = (stock) => {
    setSelectedStock(stock);
    setAdjustmentModalVisible(true);
  };

  const handleAdjustmentSubmit = (values) => {
    adjustmentMutation.mutate({
      stock_id: selectedStock.id,
      ...values,
    });
  };

  const statsData = stats?.data?.data || {};
  const lowStockData = lowStockAlerts?.data?.data || [];

  return (
    <div>
      <Title level={2}>
        <DatabaseOutlined /> Gestion du Stock
      </Title>

      {/* Low Stock Alert */}
      {lowStockData.length > 0 && (
        <Alert
          message={`${lowStockData.length} produit(s) en stock faible`}
          description="Certains produits nécessitent un réapprovisionnement"
          type="warning"
          showIcon
          icon={<WarningOutlined />}
          style={{ marginBottom: '16px' }}
          action={
            <Button
              size="small"
              type="primary"
              onClick={() => setShowLowStock(true)}
            >
              Voir les alertes
            </Button>
          }
        />
      )}

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Valeur Stock Total"
              value={statsData.total_stock_value || 0}
              suffix="DA"
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Poids Total"
              value={statsData.total_weight || 0}
              suffix="kg"
              precision={3}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Alertes Stock"
              value={statsData.low_stock_count || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Ruptures"
              value={statsData.out_of_stock_count || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Input
              placeholder="Rechercher par produit ou lot..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Filtrer par produit"
              allowClear
              value={selectedProduct}
              onChange={setSelectedProduct}
              style={{ width: '100%' }}
            >
              {products?.data?.data?.map((product) => (
                <Option key={product.id} value={product.id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Filtrer par statut"
              allowClear
              value={showLowStock}
              onChange={setShowLowStock}
              style={{ width: '100%' }}
            >
              <Option value={false}>Tout le stock</Option>
              <Option value={true}>Stock faible uniquement</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Button
              type="primary"
              icon={<BarChartOutlined />}
              block
            >
              Rapport Stock
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={stock?.data?.data?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: stock?.data?.data?.current_page,
            total: stock?.data?.data?.total,
            pageSize: stock?.data?.data?.per_page,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} articles`,
          }}
          rowClassName={(record) => {
            const status = getStockStatus(record);
            return status.status === 'error' ? 'low-stock-row' : '';
          }}
        />
      </Card>

      {/* Adjustment Modal */}
      <Modal
        title="Ajustement de Stock"
        open={adjustmentModalVisible}
        onCancel={() => {
          setAdjustmentModalVisible(false);
          setSelectedStock(null);
          form.resetFields();
        }}
        footer={null}
      >
        {selectedStock && (
          <div>
            <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
              <Text strong>{selectedStock.product?.name}</Text>
              <br />
              <Text type="secondary">Lot: {selectedStock.reception?.lot_number}</Text>
              <br />
              <Text type="secondary">
                Stock actuel: {selectedStock.current_weight_kg} kg
              </Text>
            </div>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleAdjustmentSubmit}
            >
              <Form.Item
                name="adjustment_type"
                label="Type d'ajustement"
                rules={[{ required: true, message: 'Sélectionnez le type d\'ajustement' }]}
              >
                <Select placeholder="Sélectionnez le type">
                  <Option value="increase">Augmentation</Option>
                  <Option value="decrease">Diminution</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="quantity_kg"
                label="Quantité (kg)"
                rules={[{ required: true, message: 'Saisissez la quantité' }]}
              >
                <InputNumber
                  min={0.001}
                  precision={3}
                  style={{ width: '100%' }}
                  placeholder="Quantité en kg"
                />
              </Form.Item>

              <Form.Item
                name="reason"
                label="Raison"
                rules={[{ required: true, message: 'Saisissez la raison de l\'ajustement' }]}
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Raison de l'ajustement..."
                />
              </Form.Item>

              <div style={{ textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setAdjustmentModalVisible(false)}>
                    Annuler
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={adjustmentMutation.isLoading}
                  >
                    Effectuer l'ajustement
                  </Button>
                </Space>
              </div>
            </Form>
          </div>
        )}
      </Modal>

      <style jsx>{`
        .low-stock-row {
          background-color: #fff2f0 !important;
        }
      `}</style>
    </div>
  );
};

export default Stock;
