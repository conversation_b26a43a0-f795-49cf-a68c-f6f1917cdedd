<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'phone' => '+213555000001',
            'address' => 'Abattoir Salem, Algérie',
        ]);

        // Create Storekeeper
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('ahmed123'),
            'role' => 'storekeeper',
            'phone' => '+213555000002',
            'address' => 'Abattoir Salem, Algérie',
        ]);

        // Create Salesperson
        User::create([
            'name' => 'Fatima Commerciale',
            'email' => '<EMAIL>',
            'password' => Hash::make('fatima123'),
            'role' => 'salesperson',
            'phone' => '+213555000003',
            'address' => 'Abattoir Salem, Algérie',
        ]);

        // Create Delivery Person
        User::create([
            'name' => 'Mohamed Livreur',
            'email' => '<EMAIL>',
            'password' => Hash::make('mohamed123'),
            'role' => 'delivery',
            'phone' => '+213555000004',
            'address' => 'Abattoir Salem, Algérie',
        ]);
    }
}
