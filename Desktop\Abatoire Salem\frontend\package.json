{"name": "abattoir-salem-frontend", "version": "1.0.0", "description": "Abattoir Salem Management System - Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "antd": "^5.3.0", "@ant-design/icons": "^5.0.1", "dayjs": "^1.11.7", "recharts": "^2.5.0", "react-query": "^3.39.3", "react-hook-form": "^7.43.5", "react-table": "^7.8.0", "styled-components": "^5.3.9", "lodash": "^4.17.21", "moment": "^2.29.4", "react-to-print": "^2.14.12", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "qrcode.react": "^3.1.0", "react-barcode": "^1.4.6"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/lodash": "^4.14.191", "@types/file-saver": "^2.0.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:prod": "npm run build && npm run optimize", "optimize": "npx webpack-bundle-analyzer build/static/js/*.js", "serve": "npx serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}