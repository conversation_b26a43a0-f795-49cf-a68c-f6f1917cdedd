import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Typography,
  Space,
  Drawer,
} from 'antd';
import {
  DashboardOutlined,
  InboxOutlined,
  DatabaseOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TruckOutlined,
  CreditCardOutlined,
  SearchOutlined,
  BarChartOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../contexts/NotificationContext';
import NotificationPanel from './NotificationPanel';

const { Header, Sider, Content } = AntLayout;
const { Title, Text } = Typography;

const Layout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [notificationVisible, setNotificationVisible] = useState(false);
  const { user, logout, canAccess } = useAuth();
  const { unreadCount } = useNotifications();
  const navigate = useNavigate();
  const location = useLocation();

  // Menu items based on user permissions
  const getMenuItems = () => {
    const items = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: 'Tableau de bord',
      },
    ];

    if (canAccess('reception')) {
      items.push({
        key: '/reception',
        icon: <InboxOutlined />,
        label: 'Réception',
      });
    }

    if (canAccess('stock')) {
      items.push({
        key: '/stock',
        icon: <DatabaseOutlined />,
        label: 'Stock',
      });
    }

    if (canAccess('orders')) {
      items.push({
        key: '/orders',
        icon: <ShoppingCartOutlined />,
        label: 'Commandes',
      });
    }

    if (canAccess('customers')) {
      items.push({
        key: '/customers',
        icon: <UserOutlined />,
        label: 'Clients',
      });
    }

    if (canAccess('deliveries')) {
      items.push({
        key: '/deliveries',
        icon: <TruckOutlined />,
        label: 'Livraisons',
      });
    }

    if (canAccess('payments')) {
      items.push({
        key: '/payments',
        icon: <CreditCardOutlined />,
        label: 'Paiements',
      });
    }

    if (canAccess('traceability')) {
      items.push({
        key: '/traceability',
        icon: <SearchOutlined />,
        label: 'Traçabilité',
      });
    }

    if (canAccess('reports')) {
      items.push({
        key: '/reports',
        icon: <BarChartOutlined />,
        label: 'Rapports',
      });
    }

    if (canAccess('users')) {
      items.push({
        key: '/users',
        icon: <TeamOutlined />,
        label: 'Utilisateurs',
      });
    }

    items.push({
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Paramètres',
    });

    return items;
  };

  // User menu items
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Mon Profil',
      onClick: () => navigate('/settings/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Paramètres',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Déconnexion',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const getRoleDisplayName = (role) => {
    const roleNames = {
      admin: 'Administrateur',
      storekeeper: 'Magasinier',
      salesperson: 'Commercial',
      delivery: 'Livreur',
    };
    return roleNames[role] || role;
  };

  const siderContent = (
    <>
      <div style={{
        height: '64px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottom: '1px solid #34495e',
        marginBottom: '16px',
      }}>
        <Title level={4} style={{ color: 'white', margin: 0 }}>
          {collapsed ? 'AS' : 'Abattoir Salem'}
        </Title>
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={getMenuItems()}
        onClick={handleMenuClick}
        style={{ borderRight: 0 }}
      />
    </>
  );

  return (
    <AntLayout className="main-layout">
      {/* Desktop Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint="lg"
        collapsedWidth="80"
        width={250}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100,
        }}
        className="desktop-sider"
      >
        {siderContent}
      </Sider>

      {/* Mobile Drawer */}
      <Drawer
        title="Menu"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        bodyStyle={{ padding: 0 }}
        className="mobile-drawer"
      >
        <div style={{ backgroundColor: '#34495e', minHeight: '100%' }}>
          {siderContent}
        </div>
      </Drawer>

      <AntLayout style={{ marginLeft: collapsed ? 80 : 250 }}>
        <Header
          style={{
            padding: '0 24px',
            background: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            position: 'sticky',
            top: 0,
            zIndex: 99,
          }}
        >
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuOutlined /> : <MenuOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="desktop-only"
            />
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuVisible(true)}
              className="mobile-only"
            />
          </Space>

          <Space size="large">
            {/* Notifications */}
            <Badge count={unreadCount} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                onClick={() => setNotificationVisible(true)}
                style={{ fontSize: '16px' }}
              />
            </Badge>

            {/* User Menu */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  style={{
                    backgroundColor: '#2c3e50',
                    color: 'white',
                  }}
                  icon={<UserOutlined />}
                >
                  {user?.name?.charAt(0)?.toUpperCase()}
                </Avatar>
                <div className="desktop-only">
                  <div>
                    <Text strong>{user?.name}</Text>
                  </div>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {getRoleDisplayName(user?.role)}
                    </Text>
                  </div>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content className="main-content">
          <Outlet />
        </Content>
      </AntLayout>

      {/* Notification Panel */}
      <NotificationPanel
        visible={notificationVisible}
        onClose={() => setNotificationVisible(false)}
      />

      <style jsx>{`
        @media (max-width: 992px) {
          .desktop-sider {
            display: none !important;
          }
          .ant-layout {
            margin-left: 0 !important;
          }
        }
        
        @media (min-width: 993px) {
          .mobile-drawer {
            display: none !important;
          }
        }
        
        @media (max-width: 768px) {
          .desktop-only {
            display: none !important;
          }
        }
        
        @media (min-width: 769px) {
          .mobile-only {
            display: none !important;
          }
        }
      `}</style>
    </AntLayout>
  );
};

export default Layout;
