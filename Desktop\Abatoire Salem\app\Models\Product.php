<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'animal_type',
        'description',
        'unit_price',
        'average_weight',
        'is_active',
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'average_weight' => 'decimal:3',
        'is_active' => 'boolean',
    ];

    /**
     * Get product's receptions
     */
    public function receptions()
    {
        return $this->hasMany(Reception::class);
    }

    /**
     * Get product's stock entries
     */
    public function stock()
    {
        return $this->hasMany(Stock::class);
    }

    /**
     * Get product's order items
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get total available stock weight
     */
    public function getTotalStockAttribute()
    {
        return $this->stock()
            ->where('is_available', true)
            ->sum('current_weight_kg');
    }

    /**
     * Get total reserved stock weight
     */
    public function getTotalReservedAttribute()
    {
        return $this->stock()
            ->where('is_available', true)
            ->sum('reserved_weight_kg');
    }

    /**
     * Get available stock weight (total - reserved)
     */
    public function getAvailableStockAttribute()
    {
        return $this->total_stock - $this->total_reserved;
    }

    /**
     * Check if product has low stock
     */
    public function hasLowStock(): bool
    {
        $minAlert = $this->stock()->min('minimum_alert_kg') ?? 10;
        return $this->available_stock <= $minAlert;
    }

    /**
     * Get stock status
     */
    public function getStockStatusAttribute(): string
    {
        $available = $this->available_stock;
        
        if ($available <= 0) {
            return 'out_of_stock';
        } elseif ($this->hasLowStock()) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for products with stock
     */
    public function scopeWithStock($query)
    {
        return $query->whereHas('stock', function ($q) {
            $q->where('is_available', true)
              ->where('current_weight_kg', '>', 0);
        });
    }

    /**
     * Scope for products by animal type
     */
    public function scopeByAnimalType($query, $animalType)
    {
        return $query->where('animal_type', $animalType);
    }
}
