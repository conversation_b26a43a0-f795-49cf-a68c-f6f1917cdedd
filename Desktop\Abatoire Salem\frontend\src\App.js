import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ConfigProvider } from 'antd';
import frFR from 'antd/locale/fr_FR';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import 'antd/dist/reset.css';
import './App.css';

// Context
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';

// Components
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout/Layout';
import Login from './pages/Auth/Login';

// Pages
import Dashboard from './pages/Dashboard/Dashboard';
import Reception from './pages/Reception/Reception';
import Stock from './pages/Stock/Stock';
import Orders from './pages/Orders/Orders';
import Customers from './pages/Customers/Customers';
import Deliveries from './pages/Deliveries/Deliveries';
import Payments from './pages/Payments/Payments';
import Traceability from './pages/Traceability/Traceability';
import Reports from './pages/Reports/Reports';
import Users from './pages/Users/<USER>';
import Settings from './pages/Settings/Settings';

// Configure dayjs
dayjs.locale('fr');

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Ant Design theme configuration
const theme = {
  token: {
    colorPrimary: '#2c3e50',
    colorSuccess: '#27ae60',
    colorWarning: '#f39c12',
    colorError: '#e74c3c',
    colorInfo: '#3498db',
    borderRadius: 6,
    fontFamily: '"Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Layout: {
      headerBg: '#2c3e50',
      siderBg: '#34495e',
    },
    Menu: {
      darkItemBg: '#34495e',
      darkItemSelectedBg: '#2c3e50',
    },
    Button: {
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
    },
    Table: {
      borderRadius: 8,
    },
  },
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider locale={frFR} theme={theme}>
        <AuthProvider>
          <NotificationProvider>
            <Router>
              <div className="App">
                <Routes>
                  {/* Public Routes */}
                  <Route path="/login" element={<Login />} />
                  
                  {/* Protected Routes */}
                  <Route path="/" element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }>
                    <Route index element={<Navigate to="/dashboard" replace />} />
                    <Route path="dashboard" element={<Dashboard />} />
                    
                    {/* Reception Routes (Storekeeper, Admin) */}
                    <Route path="reception/*" element={
                      <ProtectedRoute roles={['admin', 'storekeeper']}>
                        <Reception />
                      </ProtectedRoute>
                    } />
                    
                    {/* Stock Routes (All users can view) */}
                    <Route path="stock/*" element={<Stock />} />
                    
                    {/* Order Routes (Salesperson, Admin) */}
                    <Route path="orders/*" element={
                      <ProtectedRoute roles={['admin', 'salesperson']}>
                        <Orders />
                      </ProtectedRoute>
                    } />
                    
                    {/* Customer Routes (Salesperson, Admin) */}
                    <Route path="customers/*" element={
                      <ProtectedRoute roles={['admin', 'salesperson']}>
                        <Customers />
                      </ProtectedRoute>
                    } />
                    
                    {/* Delivery Routes (Delivery, Admin) */}
                    <Route path="deliveries/*" element={
                      <ProtectedRoute roles={['admin', 'delivery']}>
                        <Deliveries />
                      </ProtectedRoute>
                    } />
                    
                    {/* Payment Routes (Salesperson, Admin) */}
                    <Route path="payments/*" element={
                      <ProtectedRoute roles={['admin', 'salesperson']}>
                        <Payments />
                      </ProtectedRoute>
                    } />
                    
                    {/* Traceability Routes (All users) */}
                    <Route path="traceability/*" element={<Traceability />} />
                    
                    {/* Reports Routes (Admin, Salesperson) */}
                    <Route path="reports/*" element={
                      <ProtectedRoute roles={['admin', 'salesperson']}>
                        <Reports />
                      </ProtectedRoute>
                    } />
                    
                    {/* User Management Routes (Admin only) */}
                    <Route path="users/*" element={
                      <ProtectedRoute roles={['admin']}>
                        <Users />
                      </ProtectedRoute>
                    } />
                    
                    {/* Settings Routes (All users) */}
                    <Route path="settings/*" element={<Settings />} />
                  </Route>
                  
                  {/* Catch all route */}
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </div>
            </Router>
          </NotificationProvider>
        </AuthProvider>
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
