import React from 'react';
import { Row, Col, Card, Statistic, Alert, Typography, Space, Spin } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  DatabaseOutlined,
  UserOutlined,
  WarningOutlined,
  TruckOutlined,
  InboxOutlined,
  CreditCardOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { dashboardAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import StatsChart from './components/StatsChart';
import RecentActivities from './components/RecentActivities';
import LowStockAlerts from './components/LowStockAlerts';
import TopCustomers from './components/TopCustomers';

const { Title } = Typography;

const Dashboard = () => {
  const { user, canAccess } = useAuth();

  // Fetch dashboard statistics
  const { data: stats, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    dashboardAPI.getStats,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch daily statistics for charts
  const { data: dailyStats, isLoading: dailyStatsLoading } = useQuery(
    'dailyStats',
    () => dashboardAPI.getDailyStats({ days: 7 }),
    {
      refetchInterval: 60000, // Refetch every minute
    }
  );

  if (statsLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  const dashboardData = stats?.data?.data || {};
  const { today, this_month, general, order_status, payment_status } = dashboardData;

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = 'Bonjour';
    
    if (hour < 12) greeting = 'Bonjour';
    else if (hour < 18) greeting = 'Bon après-midi';
    else greeting = 'Bonsoir';

    return `${greeting}, ${user?.name}`;
  };

  const getRoleSpecificStats = () => {
    const roleStats = [];

    if (canAccess('orders')) {
      roleStats.push(
        <Col xs={24} sm={12} lg={6} key="orders">
          <Card className="stats-card">
            <Statistic
              title="Commandes aujourd'hui"
              value={today?.orders || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      );
    }

    if (canAccess('payments')) {
      roleStats.push(
        <Col xs={24} sm={12} lg={6} key="payments">
          <Card className="stats-card">
            <Statistic
              title="Paiements aujourd'hui"
              value={today?.payments || 0}
              prefix={<CreditCardOutlined />}
              suffix="DA"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      );
    }

    if (canAccess('deliveries')) {
      roleStats.push(
        <Col xs={24} sm={12} lg={6} key="deliveries">
          <Card className="stats-card">
            <Statistic
              title="Livraisons aujourd'hui"
              value={today?.deliveries || 0}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      );
    }

    if (canAccess('reception')) {
      roleStats.push(
        <Col xs={24} sm={12} lg={6} key="receptions">
          <Card className="stats-card">
            <Statistic
              title="Réceptions aujourd'hui"
              value={today?.receptions || 0}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      );
    }

    return roleStats;
  };

  return (
    <div>
      {/* Welcome Section */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>{getWelcomeMessage()}</Title>
        <Alert
          message="Tableau de bord en temps réel"
          description="Consultez les statistiques et activités de votre abattoir en temps réel."
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      </div>

      {/* Main Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {getRoleSpecificStats()}
      </Row>

      {/* General Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="Clients actifs"
              value={general?.total_customers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="Stock total"
              value={general?.total_stock_value || 0}
              prefix={<DatabaseOutlined />}
              suffix="DA"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="Commandes en attente"
              value={general?.pending_orders || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="Alertes stock"
              value={general?.low_stock_alerts || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts and Analytics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <StatsChart 
            data={dailyStats?.data?.data} 
            loading={dailyStatsLoading}
          />
        </Col>
        
        <Col xs={24} lg={12}>
          <TopCustomers />
        </Col>
      </Row>

      {/* Alerts and Activities */}
      <Row gutter={[16, 16]}>
        {canAccess('stock') && (
          <Col xs={24} lg={12}>
            <LowStockAlerts />
          </Col>
        )}
        
        <Col xs={24} lg={canAccess('stock') ? 12 : 24}>
          <RecentActivities />
        </Col>
      </Row>

      {/* Monthly Summary */}
      {canAccess('reports') && (
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="Résumé mensuel" style={{ textAlign: 'center' }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="Commandes ce mois"
                    value={this_month?.orders || 0}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="Chiffre d'affaires"
                    value={this_month?.sales_amount || 0}
                    suffix="DA"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="Poids total reçu"
                    value={this_month?.reception_weight || 0}
                    suffix="kg"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
