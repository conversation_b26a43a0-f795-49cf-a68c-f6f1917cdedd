<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') - {{ $company['name'] }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .header {
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 11px;
            color: #666;
        }
        
        .document-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
            text-transform: uppercase;
        }
        
        .document-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .document-info .left,
        .document-info .right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        
        .info-box {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        
        .info-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .info-content {
            font-size: 11px;
            line-height: 1.5;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .table th {
            background-color: #2c3e50;
            color: white;
            font-weight: bold;
            font-size: 11px;
        }
        
        .table td {
            font-size: 10px;
        }
        
        .table .number {
            text-align: right;
        }
        
        .table .center {
            text-align: center;
        }
        
        .totals {
            margin-top: 20px;
            text-align: right;
        }
        
        .total-row {
            margin: 5px 0;
            font-size: 12px;
        }
        
        .total-row.grand-total {
            font-weight: bold;
            font-size: 14px;
            color: #2c3e50;
            border-top: 2px solid #2c3e50;
            padding-top: 5px;
        }
        
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            border-top: 1px solid #ddd;
            padding: 10px;
            font-size: 10px;
            color: #666;
            text-align: center;
        }
        
        .page-break {
            page-break-after: always;
        }
        
        .signature-section {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        
        .signature-box {
            display: table-cell;
            width: 33%;
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            margin: 0 5px;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 40px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
            font-size: 10px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-pending { background-color: #f39c12; color: white; }
        .status-confirmed { background-color: #3498db; color: white; }
        .status-preparing { background-color: #9b59b6; color: white; }
        .status-prepared { background-color: #2ecc71; color: white; }
        .status-delivered { background-color: #27ae60; color: white; }
        .status-cancelled { background-color: #e74c3c; color: white; }
        .status-paid { background-color: #27ae60; color: white; }
        .status-unpaid { background-color: #e74c3c; color: white; }
        .status-partial { background-color: #f39c12; color: white; }
        
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        
        .font-bold { font-weight: bold; }
        .font-small { font-size: 10px; }
        .font-large { font-size: 14px; }
        
        .mt-10 { margin-top: 10px; }
        .mt-20 { margin-top: 20px; }
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">{{ $company['name'] }}</div>
            <div class="company-details">
                {{ $company['address'] }} | 
                Tél: {{ $company['phone'] }} | 
                Email: {{ $company['email'] }}
            </div>
        </div>
        
        <div class="document-title">
            @yield('document-title')
        </div>
    </div>

    <div class="content">
        @yield('content')
    </div>

    <div class="footer">
        <div>
            Document généré le {{ $generated_at->format('d/m/Y à H:i') }} par {{ $generated_by }}
        </div>
    </div>
</body>
</html>
