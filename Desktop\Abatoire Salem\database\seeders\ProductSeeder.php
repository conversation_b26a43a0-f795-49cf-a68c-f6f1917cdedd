<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            // Chicken Products
            [
                'name' => 'Poulet Vidé',
                'animal_type' => 'Poulet',
                'description' => 'Poulet entier vidé et nettoyé',
                'unit_price' => 450.00, // DZD per kg
                'average_weight' => 1.500, // 1.5 kg average
            ],
            [
                'name' => 'Poulet Entier',
                'animal_type' => 'Poulet',
                'description' => 'Poulet entier non vidé',
                'unit_price' => 400.00,
                'average_weight' => 1.800,
            ],
            [
                'name' => 'Filets de Poulet',
                'animal_type' => 'Poulet',
                'description' => 'Filets de poulet désossés',
                'unit_price' => 650.00,
                'average_weight' => 0.500,
            ],

            // Sheep Products
            [
                'name' => 'Mouton Entier',
                'animal_type' => 'Mouton',
                'description' => 'Mouton entier découpé',
                'unit_price' => 1200.00,
                'average_weight' => 25.000,
            ],
            [
                'name' => 'Gigot de Mouton',
                'animal_type' => 'Mouton',
                'description' => 'Gigot de mouton',
                'unit_price' => 1400.00,
                'average_weight' => 3.500,
            ],
            [
                'name' => 'Côtelettes de Mouton',
                'animal_type' => 'Mouton',
                'description' => 'Côtelettes de mouton',
                'unit_price' => 1500.00,
                'average_weight' => 1.200,
            ],

            // Turkey Products
            [
                'name' => 'Dinde Entière',
                'animal_type' => 'Dinde',
                'description' => 'Dinde entière vidée',
                'unit_price' => 550.00,
                'average_weight' => 4.000,
            ],
            [
                'name' => 'Escalope de Dinde',
                'animal_type' => 'Dinde',
                'description' => 'Escalope de dinde',
                'unit_price' => 750.00,
                'average_weight' => 0.300,
            ],

            // Beef Products
            [
                'name' => 'Viande de Bœuf',
                'animal_type' => 'Bœuf',
                'description' => 'Viande de bœuf découpée',
                'unit_price' => 1800.00,
                'average_weight' => 2.000,
            ],
            [
                'name' => 'Entrecôte de Bœuf',
                'animal_type' => 'Bœuf',
                'description' => 'Entrecôte de bœuf',
                'unit_price' => 2200.00,
                'average_weight' => 0.800,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
