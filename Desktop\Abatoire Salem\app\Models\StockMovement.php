<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'stock_id',
        'user_id',
        'movement_type',
        'quantity_kg',
        'balance_after',
        'reference_type',
        'reference_id',
        'reason',
        'movement_date',
    ];

    protected $casts = [
        'quantity_kg' => 'decimal:3',
        'balance_after' => 'decimal:3',
        'movement_date' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the related reference model (polymorphic)
     */
    public function reference()
    {
        switch ($this->reference_type) {
            case 'order':
                return $this->belongsTo(Order::class, 'reference_id');
            case 'reception':
                return $this->belongsTo(Reception::class, 'reference_id');
            default:
                return null;
        }
    }

    /**
     * Scopes
     */
    public function scopeByStock($query, $stockId)
    {
        return $query->where('stock_id', $stockId);
    }

    public function scopeByMovementType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('movement_date', '>=', now()->subDays($days));
    }
}
