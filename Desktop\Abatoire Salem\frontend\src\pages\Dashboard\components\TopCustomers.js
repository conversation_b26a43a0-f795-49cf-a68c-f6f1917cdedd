import React, { useState } from 'react';
import { Card, List, Avatar, Typography, Select, Spin, Empty, Tag } from 'antd';
import { UserOutlined, CrownOutlined } from '@ant-design/icons';
import { useQuery } from 'react-query';
import { dashboardAPI } from '../../../services/api';

const { Text } = Typography;
const { Option } = Select;

const TopCustomers = () => {
  const [period, setPeriod] = useState('month');

  const { data, isLoading } = useQuery(
    ['topCustomers', period],
    () => dashboardAPI.getTopCustomers({ period, limit: 5 }),
    {
      refetchInterval: 60000, // Refetch every minute
    }
  );

  const customers = data?.data?.data || [];

  const getPeriodLabel = (period) => {
    const labels = {
      month: 'ce mois',
      quarter: 'ce trimestre',
      year: 'cette année',
      all: 'toute période',
    };
    return labels[period] || 'ce mois';
  };

  const getCustomerRank = (index) => {
    const ranks = ['🥇', '🥈', '🥉'];
    return ranks[index] || `${index + 1}`;
  };

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card
      title="Top Clients"
      className="chart-container"
      extra={
        <Select
          value={period}
          onChange={setPeriod}
          style={{ width: 120 }}
          size="small"
        >
          <Option value="month">Ce mois</Option>
          <Option value="quarter">Trimestre</Option>
          <Option value="year">Cette année</Option>
          <Option value="all">Tout</Option>
        </Select>
      }
    >
      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : customers.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Aucun client trouvé"
        />
      ) : (
        <>
          <Text type="secondary" style={{ marginBottom: '16px', display: 'block' }}>
            Classement pour {getPeriodLabel(period)}
          </Text>
          
          <List
            itemLayout="horizontal"
            dataSource={customers}
            renderItem={(customer, index) => (
              <List.Item
                style={{
                  padding: '12px 0',
                  borderBottom: index === customers.length - 1 ? 'none' : '1px solid #f0f0f0',
                }}
              >
                <List.Item.Meta
                  avatar={
                    <div style={{ position: 'relative' }}>
                      <Avatar
                        style={{
                          backgroundColor: index === 0 ? '#faad14' : '#1890ff',
                          color: 'white',
                        }}
                        icon={<UserOutlined />}
                      >
                        {customer.customer?.name?.charAt(0)?.toUpperCase()}
                      </Avatar>
                      {index < 3 && (
                        <div
                          style={{
                            position: 'absolute',
                            top: '-5px',
                            right: '-5px',
                            fontSize: '16px',
                          }}
                        >
                          {getCustomerRank(index)}
                        </div>
                      )}
                    </div>
                  }
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Text strong>{customer.customer?.name}</Text>
                      {index === 0 && (
                        <CrownOutlined style={{ color: '#faad14' }} />
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">
                          {customer.orders_count} commande{customer.orders_count > 1 ? 's' : ''}
                        </Text>
                      </div>
                      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                        <Tag color="green">
                          {formatAmount(customer.total_amount)}
                        </Tag>
                        {customer.unpaid_amount > 0 && (
                          <Tag color="orange">
                            Impayé: {formatAmount(customer.unpaid_amount)}
                          </Tag>
                        )}
                      </div>
                      <div style={{ marginTop: '4px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          Panier moyen: {formatAmount(customer.average_order_value)}
                        </Text>
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </>
      )}
    </Card>
  );
};

export default TopCustomers;
