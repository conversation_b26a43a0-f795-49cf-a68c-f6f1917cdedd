import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FilePdfOutlined,
  InboxOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { receptionAPI, productsAPI } from '../../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Reception = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReception, setEditingReception] = useState(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();

  // Fetch receptions
  const { data: receptions, isLoading } = useQuery(
    ['receptions', { searchText, selectedProduct, dateRange }],
    () => receptionAPI.getReceptions({
      search: searchText,
      product_id: selectedProduct,
      start_date: dateRange?.[0]?.format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD'),
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch products for filter
  const { data: products } = useQuery('products', productsAPI.getProducts);

  // Fetch statistics
  const { data: stats } = useQuery(
    ['receptionStats', dateRange],
    () => receptionAPI.getStatistics({
      start_date: dateRange?.[0]?.format('YYYY-MM-DD') || dayjs().startOf('month').format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD') || dayjs().endOf('month').format('YYYY-MM-DD'),
    })
  );

  // Create reception mutation
  const createMutation = useMutation(receptionAPI.createReception, {
    onSuccess: () => {
      message.success('Réception créée avec succès');
      setModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('receptions');
      queryClient.invalidateQueries('receptionStats');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de la création');
    },
  });

  // Update reception mutation
  const updateMutation = useMutation(
    ({ id, data }) => receptionAPI.updateReception(id, data),
    {
      onSuccess: () => {
        message.success('Réception mise à jour avec succès');
        setModalVisible(false);
        setEditingReception(null);
        form.resetFields();
        queryClient.invalidateQueries('receptions');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Erreur lors de la mise à jour');
      },
    }
  );

  // Delete reception mutation
  const deleteMutation = useMutation(receptionAPI.deleteReception, {
    onSuccess: () => {
      message.success('Réception supprimée avec succès');
      queryClient.invalidateQueries('receptions');
      queryClient.invalidateQueries('receptionStats');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de la suppression');
    },
  });

  const columns = [
    {
      title: 'N° Lot',
      dataIndex: 'lot_number',
      key: 'lot_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Produit',
      dataIndex: ['product', 'name'],
      key: 'product',
    },
    {
      title: 'Type Animal',
      dataIndex: ['product', 'animal_type'],
      key: 'animal_type',
    },
    {
      title: 'Quantité (têtes)',
      dataIndex: 'quantity_heads',
      key: 'quantity_heads',
      align: 'center',
    },
    {
      title: 'Poids Total (kg)',
      dataIndex: 'total_weight_kg',
      key: 'total_weight_kg',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Poids Moyen (kg)',
      dataIndex: 'average_weight_kg',
      key: 'average_weight_kg',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Date Réception',
      dataIndex: 'reception_date',
      key: 'reception_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Reçu par',
      dataIndex: ['user', 'name'],
      key: 'user',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/reception/${record.id}`)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            icon={<FilePdfOutlined />}
            onClick={() => handleDownloadPDF(record.id)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  const handleEdit = (reception) => {
    setEditingReception(reception);
    form.setFieldsValue({
      ...reception,
      reception_date: dayjs(reception.reception_date),
      slaughter_date: dayjs(reception.slaughter_date),
    });
    setModalVisible(true);
  };

  const handleDelete = (id) => {
    Modal.confirm({
      title: 'Confirmer la suppression',
      content: 'Êtes-vous sûr de vouloir supprimer cette réception ?',
      okText: 'Supprimer',
      cancelText: 'Annuler',
      okType: 'danger',
      onOk: () => deleteMutation.mutate(id),
    });
  };

  const handleDownloadPDF = async (id) => {
    try {
      const response = await receptionAPI.downloadPDF(id);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `reception_${id}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      message.error('Erreur lors du téléchargement du PDF');
    }
  };

  const handleSubmit = (values) => {
    const data = {
      ...values,
      reception_date: values.reception_date.format('YYYY-MM-DD'),
      slaughter_date: values.slaughter_date.format('YYYY-MM-DD'),
    };

    if (editingReception) {
      updateMutation.mutate({ id: editingReception.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const statsData = stats?.data?.data || {};

  return (
    <div>
      <Title level={2}>
        <InboxOutlined /> Gestion des Réceptions
      </Title>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Réceptions"
              value={statsData.total_receptions || 0}
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Poids Total (kg)"
              value={statsData.total_weight || 0}
              precision={3}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Têtes"
              value={statsData.total_heads || 0}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Input
              placeholder="Rechercher par lot ou origine..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Filtrer par produit"
              allowClear
              value={selectedProduct}
              onChange={setSelectedProduct}
              style={{ width: '100%' }}
            >
              {products?.data?.data?.map((product) => (
                <Option key={product.id} value={product.id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
              block
            >
              Nouvelle Réception
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={receptions?.data?.data?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: receptions?.data?.data?.current_page,
            total: receptions?.data?.data?.total,
            pageSize: receptions?.data?.data?.per_page,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} réceptions`,
          }}
        />
      </Card>

      {/* Modal */}
      <Modal
        title={editingReception ? 'Modifier la Réception' : 'Nouvelle Réception'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingReception(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="product_id"
                label="Produit"
                rules={[{ required: true, message: 'Sélectionnez un produit' }]}
              >
                <Select placeholder="Sélectionnez un produit">
                  {products?.data?.data?.map((product) => (
                    <Option key={product.id} value={product.id}>
                      {product.name} ({product.animal_type})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="origin"
                label="Origine"
              >
                <Input placeholder="Origine de la marchandise" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="quantity_heads"
                label="Quantité (têtes)"
                rules={[{ required: true, message: 'Saisissez la quantité' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="Nombre de têtes"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="total_weight_kg"
                label="Poids Total (kg)"
                rules={[{ required: true, message: 'Saisissez le poids total' }]}
              >
                <InputNumber
                  min={0.001}
                  precision={3}
                  style={{ width: '100%' }}
                  placeholder="Poids en kg"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="slaughter_date"
                label="Date d'Abattage"
                rules={[{ required: true, message: 'Sélectionnez la date d\'abattage' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="reception_date"
                label="Date de Réception"
                rules={[{ required: true, message: 'Sélectionnez la date de réception' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="notes"
                label="Notes"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Notes additionnelles..."
                />
              </Form.Item>
            </Col>
          </Row>
          
          <div style={{ textAlign: 'right', marginTop: '16px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Annuler
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isLoading || updateMutation.isLoading}
              >
                {editingReception ? 'Modifier' : 'Créer'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default Reception;
