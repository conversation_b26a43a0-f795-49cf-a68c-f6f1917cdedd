<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_notes', function (Blueprint $table) {
            $table->id();
            $table->string('delivery_number')->unique(); // e.g., "LIV20250726001"
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('preparation_slip_id')->constrained()->onDelete('cascade');
            $table->foreignId('delivered_by')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('status', ['pending', 'in_transit', 'delivered', 'returned'])->default('pending');
            $table->datetime('delivery_date');
            $table->datetime('delivered_at')->nullable();
            $table->string('customer_signature')->nullable(); // Path to signature image
            $table->text('delivery_notes')->nullable();
            $table->text('return_reason')->nullable(); // If returned
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_notes');
    }
};
