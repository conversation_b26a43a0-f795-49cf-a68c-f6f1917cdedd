/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
}

/* Layout Styles */
.main-layout {
  min-height: 100vh;
}

.main-content {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* Card Styles */
.stats-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-card .ant-card-body {
  padding: 20px;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-confirmed {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #74c0fc;
}

.status-preparing {
  background-color: #e2e3ff;
  color: #4c63d2;
  border: 1px solid #a29bfe;
}

.status-prepared {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #00b894;
}

.status-delivered {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #00b894;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #e74c3c;
}

.status-paid {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #00b894;
}

.status-unpaid {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #e74c3c;
}

.status-partial {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Priority Badges */
.priority-high {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #e74c3c;
}

.priority-medium {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 1px solid #f39c12;
}

.priority-low {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #27ae60;
}

/* Table Styles */
.data-table .ant-table-thead > tr > th {
  background-color: #2c3e50;
  color: white;
  font-weight: 600;
  border-bottom: 2px solid #34495e;
}

.data-table .ant-table-tbody > tr:hover > td {
  background-color: #f8f9fa;
}

.data-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #e9ecef;
}

/* Form Styles */
.form-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.form-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}

/* Button Styles */
.btn-primary {
  background-color: #2c3e50;
  border-color: #2c3e50;
}

.btn-primary:hover {
  background-color: #34495e;
  border-color: #34495e;
}

.btn-success {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-success:hover {
  background-color: #2ecc71;
  border-color: #2ecc71;
}

.btn-warning {
  background-color: #f39c12;
  border-color: #f39c12;
}

.btn-warning:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

.btn-danger {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

/* Chart Styles */
.chart-container {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  text-align: center;
}

/* Alert Styles */
.alert-container {
  margin-bottom: 24px;
}

.low-stock-alert {
  border-left: 4px solid #e74c3c;
  background-color: #fff5f5;
}

.credit-alert {
  border-left: 4px solid #f39c12;
  background-color: #fffbf0;
}

/* Loading Styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .stats-number {
    font-size: 24px;
  }
  
  .form-section {
    padding: 16px;
  }
  
  .chart-container {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: 12px;
  }
  
  .stats-number {
    font-size: 20px;
  }
  
  .form-section {
    padding: 12px;
  }
  
  .chart-container {
    padding: 12px;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white;
  }
  
  .main-content {
    padding: 0;
    background: white;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: normal; }

.text-primary { color: #2c3e50; }
.text-success { color: #27ae60; }
.text-warning { color: #f39c12; }
.text-danger { color: #e74c3c; }
.text-info { color: #3498db; }
.text-muted { color: #6c757d; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }
