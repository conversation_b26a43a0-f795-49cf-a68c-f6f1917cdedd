<?php

namespace App\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Order;
use App\Models\Reception;
use App\Models\PreparationSlip;
use App\Models\DeliveryNote;
use App\Models\Payment;
use Carbon\Carbon;

class PDFService
{
    /**
     * Generate order PDF
     */
    public function generateOrderPDF(Order $order): \Barryvdh\DomPDF\PDF
    {
        $order->load(['customer', 'user', 'orderItems.product']);

        $data = [
            'order' => $order,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.order', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate reception PDF
     */
    public function generateReceptionPDF(Reception $reception): \Barryvdh\DomPDF\PDF
    {
        $reception->load(['product', 'user', 'stock']);

        $data = [
            'reception' => $reception,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.reception', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate preparation slip PDF
     */
    public function generatePreparationPDF(PreparationSlip $preparationSlip): \Barryvdh\DomPDF\PDF
    {
        $preparationSlip->load([
            'order.customer',
            'order.orderItems.product',
            'preparedBy'
        ]);

        $data = [
            'preparation' => $preparationSlip,
            'order' => $preparationSlip->order,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.preparation', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate delivery note PDF
     */
    public function generateDeliveryPDF(DeliveryNote $deliveryNote): \Barryvdh\DomPDF\PDF
    {
        $deliveryNote->load([
            'order.customer',
            'order.orderItems.product',
            'preparationSlip',
            'deliveredBy'
        ]);

        $data = [
            'delivery' => $deliveryNote,
            'order' => $deliveryNote->order,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.delivery', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate payment receipt PDF
     */
    public function generatePaymentPDF(Payment $payment): \Barryvdh\DomPDF\PDF
    {
        $payment->load(['customer', 'order', 'user']);

        $data = [
            'payment' => $payment,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.payment', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate stock report PDF
     */
    public function generateStockReportPDF(array $stockData): \Barryvdh\DomPDF\PDF
    {
        $data = [
            'stock_data' => $stockData,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.stock-report', $data)
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate sales report PDF
     */
    public function generateSalesReportPDF(array $salesData): \Barryvdh\DomPDF\PDF
    {
        $data = [
            'sales_data' => $salesData,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.sales-report', $data)
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }

    /**
     * Generate customer statement PDF
     */
    public function generateCustomerStatementPDF($customer, array $orders, array $payments): \Barryvdh\DomPDF\PDF
    {
        $data = [
            'customer' => $customer,
            'orders' => $orders,
            'payments' => $payments,
            'company' => [
                'name' => 'Abattoir Salem',
                'address' => 'Algérie',
                'phone' => '+213 XXX XXX XXX',
                'email' => '<EMAIL>',
            ],
            'generated_at' => Carbon::now(),
            'generated_by' => auth()->user()->name ?? 'System',
        ];

        return PDF::loadView('pdf.customer-statement', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
    }
}
