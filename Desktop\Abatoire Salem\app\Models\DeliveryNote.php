<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class DeliveryNote extends Model
{
    use HasFactory;

    protected $fillable = [
        'delivery_number',
        'order_id',
        'preparation_slip_id',
        'delivered_by',
        'status',
        'delivery_date',
        'delivered_at',
        'customer_signature',
        'delivery_notes',
        'return_reason',
    ];

    protected $casts = [
        'delivery_date' => 'datetime',
        'delivered_at' => 'datetime',
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Generate delivery number before creating
        static::creating(function ($delivery) {
            if (empty($delivery->delivery_number)) {
                $delivery->delivery_number = static::generateDeliveryNumber();
            }
        });
    }

    /**
     * Generate unique delivery number
     */
    public static function generateDeliveryNumber(): string
    {
        $date = Carbon::now()->format('Ymd');
        $count = static::whereDate('created_at', Carbon::today())->count() + 1;
        return "LIV{$date}" . str_pad($count, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Start delivery
     */
    public function startDelivery(int $userId): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'in_transit',
            'delivered_by' => $userId,
            'delivery_date' => now(),
        ]);

        return true;
    }

    /**
     * Complete delivery
     */
    public function completeDelivery(string $signature = null, string $notes = null): bool
    {
        if ($this->status !== 'in_transit') {
            return false;
        }

        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'customer_signature' => $signature,
            'delivery_notes' => $notes,
        ]);

        // Update order status and consume stock
        $this->order->update(['status' => 'delivered']);
        $this->order->consumeStock();

        return true;
    }

    /**
     * Return delivery
     */
    public function returnDelivery(string $reason): bool
    {
        if (!in_array($this->status, ['in_transit', 'delivered'])) {
            return false;
        }

        $this->update([
            'status' => 'returned',
            'return_reason' => $reason,
        ]);

        // Update order status and unreserve stock
        $this->order->update(['status' => 'prepared']);
        
        // If it was delivered, we need to add stock back
        if ($this->status === 'delivered') {
            // This would require more complex logic to add stock back
            // For now, we'll just unreserve
            $this->order->unreserveStock();
        }

        return true;
    }

    /**
     * Get delivery duration in minutes
     */
    public function getDeliveryDurationAttribute(): ?int
    {
        if (!$this->delivery_date || !$this->delivered_at) {
            return null;
        }

        return $this->delivery_date->diffInMinutes($this->delivered_at);
    }

    /**
     * Check if delivery is overdue
     */
    public function isOverdue(): bool
    {
        if (in_array($this->status, ['delivered', 'returned'])) {
            return false;
        }

        // Consider overdue if in transit for more than 4 hours
        return $this->delivery_date && $this->delivery_date->diffInHours(now()) > 4;
    }

    /**
     * Relationships
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function preparationSlip()
    {
        return $this->belongsTo(PreparationSlip::class);
    }

    public function deliveredBy()
    {
        return $this->belongsTo(User::class, 'delivered_by');
    }

    /**
     * Scopes
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeReturned($query)
    {
        return $query->where('status', 'returned');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'in_transit')
                    ->where('delivery_date', '<', now()->subHours(4));
    }

    public function scopeByDeliverer($query, $userId)
    {
        return $query->where('delivered_by', $userId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('delivery_date', Carbon::today());
    }
}
