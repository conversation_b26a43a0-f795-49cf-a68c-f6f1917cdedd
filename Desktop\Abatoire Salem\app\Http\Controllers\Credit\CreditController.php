<?php

namespace App\Http\Controllers\Credit;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Payment;
use App\Models\ActivityLog;
use Carbon\Carbon;

class CreditController extends Controller
{
    /**
     * Display a listing of customers
     */
    public function customers(Request $request)
    {
        $query = Customer::query();

        // Apply filters
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('with_unpaid') && $request->boolean('with_unpaid')) {
            $query->withUnpaidOrders();
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $customers = $query->paginate($perPage);

        // Add calculated fields
        $customers->getCollection()->transform(function ($customer) {
            $customer->unpaid_orders_count = $customer->unpaid_orders_count;
            $customer->total_unpaid_amount = $customer->total_unpaid_amount;
            $customer->credit_status = $customer->credit_status;
            $customer->can_place_order = $customer->canPlaceOrder();
            return $customer;
        });

        return response()->json([
            'success' => true,
            'data' => $customers,
            'message' => 'Customers retrieved successfully'
        ]);
    }

    /**
     * Store a newly created customer
     */
    public function createCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255|unique:customers,email',
            'address' => 'nullable|string|max:500',
            'credit_limit' => 'required|numeric|min:0',
            'max_unpaid_orders' => 'required|integer|min:1|max:10',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $customer = Customer::create($request->all());

            // Log activity
            ActivityLog::logActivity(
                'created',
                "Created customer: {$customer->name}",
                'Customer',
                $customer->id,
                null,
                $customer->toArray()
            );

            $customer->unpaid_orders_count = 0;
            $customer->total_unpaid_amount = 0;
            $customer->credit_status = 'good';
            $customer->can_place_order = true;

            return response()->json([
                'success' => true,
                'data' => $customer,
                'message' => 'Customer created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified customer
     */
    public function updateCustomer(Request $request, Customer $customer)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255|unique:customers,email,' . $customer->id,
            'address' => 'nullable|string|max:500',
            'credit_limit' => 'sometimes|numeric|min:0',
            'max_unpaid_orders' => 'sometimes|integer|min:1|max:10',
            'is_active' => 'sometimes|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $oldValues = $customer->toArray();
            $customer->update($request->all());

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Updated customer: {$customer->name}",
                'Customer',
                $customer->id,
                $oldValues,
                $customer->fresh()->toArray()
            );

            $customer->unpaid_orders_count = $customer->unpaid_orders_count;
            $customer->total_unpaid_amount = $customer->total_unpaid_amount;
            $customer->credit_status = $customer->credit_status;
            $customer->can_place_order = $customer->canPlaceOrder();

            return response()->json([
                'success' => true,
                'data' => $customer,
                'message' => 'Customer updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer credit status
     */
    public function creditStatus(Customer $customer)
    {
        $unpaidOrders = $customer->orders()
            ->whereIn('payment_status', ['unpaid', 'partial'])
            ->with('orderItems.product')
            ->orderBy('order_date', 'desc')
            ->get();

        $totalUnpaid = $unpaidOrders->sum(function($order) {
            return $order->total_amount - $order->paid_amount;
        });

        $creditInfo = [
            'customer' => $customer,
            'credit_limit' => $customer->credit_limit,
            'max_unpaid_orders' => $customer->max_unpaid_orders,
            'unpaid_orders_count' => $unpaidOrders->count(),
            'total_unpaid_amount' => $totalUnpaid,
            'available_credit' => max(0, $customer->credit_limit - $totalUnpaid),
            'credit_status' => $customer->credit_status,
            'can_place_order' => $customer->canPlaceOrder(),
            'unpaid_orders' => $unpaidOrders,
            'credit_utilization_percentage' => $customer->credit_limit > 0 
                ? round(($totalUnpaid / $customer->credit_limit) * 100, 2) 
                : 0,
        ];

        return response()->json([
            'success' => true,
            'data' => $creditInfo,
            'message' => 'Customer credit status retrieved successfully'
        ]);
    }

    /**
     * Get customer orders
     */
    public function customerOrders(Customer $customer, Request $request)
    {
        $query = $customer->orders()->with(['orderItems.product', 'user']);

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->byStatus($request->status);
        }

        if ($request->has('payment_status') && $request->payment_status) {
            $query->byPaymentStatus($request->payment_status);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'order_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $orders = $query->paginate($perPage);

        // Add calculated fields
        $orders->getCollection()->transform(function ($order) {
            $order->remaining_amount = $order->remaining_amount;
            return $order;
        });

        return response()->json([
            'success' => true,
            'data' => $orders,
            'message' => 'Customer orders retrieved successfully'
        ]);
    }

    /**
     * Record a payment
     */
    public function recordPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'order_id' => 'nullable|exists:orders,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference' => 'nullable|string|max:255',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate customer
        $customer = Customer::find($request->customer_id);
        if (!$customer->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Customer account is inactive'
            ], 400);
        }

        // Validate order if specified
        $order = null;
        if ($request->order_id) {
            $order = Order::find($request->order_id);
            if ($order->customer_id !== $customer->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order does not belong to this customer'
                ], 400);
            }

            if ($order->payment_status === 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order is already fully paid'
                ], 400);
            }

            // Check if payment amount exceeds remaining amount
            if ($request->amount > $order->remaining_amount) {
                return response()->json([
                    'success' => false,
                    'message' => "Payment amount exceeds remaining amount ({$order->remaining_amount})"
                ], 400);
            }
        }

        try {
            $payment = Payment::create([
                'customer_id' => $request->customer_id,
                'order_id' => $request->order_id,
                'user_id' => auth()->id(),
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'payment_date' => $request->payment_date,
                'notes' => $request->notes,
            ]);

            // Log activity
            $description = "Recorded payment of {$request->amount} for customer {$customer->name}";
            if ($order) {
                $description .= " (Order: {$order->order_number})";
            }

            ActivityLog::logActivity(
                'created',
                $description,
                'Payment',
                $payment->id,
                null,
                $payment->toArray()
            );

            $payment->load(['customer', 'order', 'user']);

            return response()->json([
                'success' => true,
                'data' => $payment,
                'message' => 'Payment recorded successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payments list
     */
    public function payments(Request $request)
    {
        $query = Payment::with(['customer', 'order', 'user']);

        // Apply filters
        if ($request->has('customer_id') && $request->customer_id) {
            $query->byCustomer($request->customer_id);
        }

        if ($request->has('order_id') && $request->order_id) {
            $query->byOrder($request->order_id);
        }

        if ($request->has('payment_method') && $request->payment_method) {
            $query->byPaymentMethod($request->payment_method);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        if ($request->has('today') && $request->boolean('today')) {
            $query->today();
        }

        if ($request->has('this_month') && $request->boolean('this_month')) {
            $query->thisMonth();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'payment_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $payments = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $payments,
            'message' => 'Payments retrieved successfully'
        ]);
    }

    /**
     * Get customer payments
     */
    public function customerPayments(Customer $customer, Request $request)
    {
        $query = $customer->payments()->with(['order', 'user']);

        // Apply filters
        if ($request->has('payment_method') && $request->payment_method) {
            $query->byPaymentMethod($request->payment_method);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'payment_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $payments = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $payments,
            'message' => 'Customer payments retrieved successfully'
        ]);
    }

    /**
     * Get credit statistics
     */
    public function statistics(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $stats = [
            'total_customers' => Customer::active()->count(),
            'customers_with_credit' => Customer::active()->withUnpaidOrders()->count(),
            'total_credit_limit' => Customer::active()->sum('credit_limit'),
            'total_unpaid_amount' => Order::unpaid()->sum('total_amount') - Order::unpaid()->sum('paid_amount'),
            'total_payments' => Payment::byDateRange($startDate, $endDate)->sum('amount'),
            'payments_count' => Payment::byDateRange($startDate, $endDate)->count(),
            'by_payment_method' => Payment::byDateRange($startDate, $endDate)
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
                ->groupBy('payment_method')
                ->get()
                ->keyBy('payment_method'),
            'customers_by_credit_status' => Customer::active()->get()->groupBy('credit_status')->map->count(),
            'top_paying_customers' => Payment::byDateRange($startDate, $endDate)
                ->with('customer')
                ->selectRaw('customer_id, COUNT(*) as payment_count, SUM(amount) as total_amount')
                ->groupBy('customer_id')
                ->orderBy('total_amount', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Credit statistics retrieved successfully'
        ]);
    }

    /**
     * Get customers at risk (high credit utilization or overdue)
     */
    public function customersAtRisk(Request $request)
    {
        $customers = Customer::active()
            ->withUnpaidOrders()
            ->get()
            ->filter(function ($customer) {
                $creditStatus = $customer->credit_status;
                $unpaidCount = $customer->unpaid_orders_count;

                return $creditStatus === 'critical' ||
                       $unpaidCount >= $customer->max_unpaid_orders ||
                       !$customer->canPlaceOrder();
            })
            ->map(function ($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'phone' => $customer->phone,
                    'email' => $customer->email,
                    'credit_limit' => $customer->credit_limit,
                    'unpaid_orders_count' => $customer->unpaid_orders_count,
                    'total_unpaid_amount' => $customer->total_unpaid_amount,
                    'credit_status' => $customer->credit_status,
                    'can_place_order' => $customer->canPlaceOrder(),
                    'risk_factors' => $this->getRiskFactors($customer),
                ];
            })
            ->values();

        return response()->json([
            'success' => true,
            'data' => $customers,
            'message' => 'Customers at risk retrieved successfully'
        ]);
    }

    /**
     * Get risk factors for a customer
     */
    private function getRiskFactors(Customer $customer): array
    {
        $factors = [];

        if ($customer->credit_status === 'critical') {
            $factors[] = 'High credit utilization';
        }

        if ($customer->unpaid_orders_count >= $customer->max_unpaid_orders) {
            $factors[] = 'Maximum unpaid orders reached';
        }

        if (!$customer->canPlaceOrder()) {
            $factors[] = 'Cannot place new orders';
        }

        // Check for old unpaid orders (more than 30 days)
        $oldUnpaidCount = $customer->orders()
            ->whereIn('payment_status', ['unpaid', 'partial'])
            ->where('order_date', '<', Carbon::now()->subDays(30))
            ->count();

        if ($oldUnpaidCount > 0) {
            $factors[] = "Has {$oldUnpaidCount} unpaid orders older than 30 days";
        }

        return $factors;
    }
}
