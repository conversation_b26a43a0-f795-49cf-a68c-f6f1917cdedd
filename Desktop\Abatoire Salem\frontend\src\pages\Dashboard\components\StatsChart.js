import React, { useState } from 'react';
import { Card, Select, Spin } from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import dayjs from 'dayjs';

const { Option } = Select;

const StatsChart = ({ data, loading }) => {
  const [chartType, setChartType] = useState('orders');
  const [viewType, setViewType] = useState('line');

  if (loading || !data) {
    return (
      <Card title="Statistiques des 7 derniers jours" className="chart-container">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  // Transform data for charts
  const chartData = data.dates?.map((date, index) => ({
    date: dayjs(date).format('DD/MM'),
    fullDate: date,
    ordersCount: data.orders?.count?.[index] || 0,
    ordersAmount: data.orders?.amount?.[index] || 0,
    paymentsCount: data.payments?.count?.[index] || 0,
    paymentsAmount: data.payments?.amount?.[index] || 0,
    receptionsCount: data.receptions?.count?.[index] || 0,
    receptionsWeight: data.receptions?.weight?.[index] || 0,
  })) || [];

  const getChartConfig = () => {
    switch (chartType) {
      case 'orders':
        return {
          title: 'Commandes',
          dataKey1: 'ordersCount',
          dataKey2: 'ordersAmount',
          label1: 'Nombre de commandes',
          label2: 'Montant (DA)',
          color1: '#1890ff',
          color2: '#52c41a',
        };
      case 'payments':
        return {
          title: 'Paiements',
          dataKey1: 'paymentsCount',
          dataKey2: 'paymentsAmount',
          label1: 'Nombre de paiements',
          label2: 'Montant (DA)',
          color1: '#722ed1',
          color2: '#eb2f96',
        };
      case 'receptions':
        return {
          title: 'Réceptions',
          dataKey1: 'receptionsCount',
          dataKey2: 'receptionsWeight',
          label1: 'Nombre de réceptions',
          label2: 'Poids (kg)',
          color1: '#fa8c16',
          color2: '#faad14',
        };
      default:
        return {
          title: 'Commandes',
          dataKey1: 'ordersCount',
          dataKey2: 'ordersAmount',
          label1: 'Nombre de commandes',
          label2: 'Montant (DA)',
          color1: '#1890ff',
          color2: '#52c41a',
        };
    }
  };

  const config = getChartConfig();

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: 'white',
            padding: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          }}
        >
          <p style={{ margin: 0, fontWeight: 'bold' }}>{`Date: ${label}`}</p>
          {payload.map((entry, index) => (
            <p
              key={index}
              style={{
                margin: '4px 0',
                color: entry.color,
              }}
            >
              {`${entry.name}: ${entry.value.toLocaleString()}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    const ChartComponent = viewType === 'line' ? LineChart : BarChart;
    const DataComponent = viewType === 'line' ? Line : Bar;

    return (
      <ResponsiveContainer width="100%" height={300}>
        <ChartComponent data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            yAxisId="left"
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            yAxisId="right" 
            orientation="right"
            tick={{ fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          <DataComponent
            yAxisId="left"
            type="monotone"
            dataKey={config.dataKey1}
            stroke={config.color1}
            fill={config.color1}
            name={config.label1}
            strokeWidth={2}
          />
          <DataComponent
            yAxisId="right"
            type="monotone"
            dataKey={config.dataKey2}
            stroke={config.color2}
            fill={config.color2}
            name={config.label2}
            strokeWidth={2}
          />
        </ChartComponent>
      </ResponsiveContainer>
    );
  };

  return (
    <Card
      title={`Statistiques - ${config.title}`}
      className="chart-container"
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Select
            value={chartType}
            onChange={setChartType}
            style={{ width: 120 }}
            size="small"
          >
            <Option value="orders">Commandes</Option>
            <Option value="payments">Paiements</Option>
            <Option value="receptions">Réceptions</Option>
          </Select>
          <Select
            value={viewType}
            onChange={setViewType}
            style={{ width: 100 }}
            size="small"
          >
            <Option value="line">Courbe</Option>
            <Option value="bar">Barres</Option>
          </Select>
        </div>
      }
    >
      {chartData.length > 0 ? (
        renderChart()
      ) : (
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          Aucune donnée disponible
        </div>
      )}
    </Card>
  );
};

export default StatsChart;
