<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Stock;
use App\Models\StockMovement;
use App\Models\Product;
use App\Models\ActivityLog;
use Carbon\Carbon;

class StockController extends Controller
{
    /**
     * Display a listing of stock
     */
    public function index(Request $request)
    {
        $query = Stock::with(['product', 'reception.user']);

        // Apply filters
        if ($request->has('product_id') && $request->product_id) {
            $query->byProduct($request->product_id);
        }

        if ($request->has('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        if ($request->has('low_stock') && $request->boolean('low_stock')) {
            $query->lowStock();
        }

        if ($request->has('out_of_stock') && $request->boolean('out_of_stock')) {
            $query->outOfStock();
        }

        // Only show available stock by default
        if (!$request->has('show_all')) {
            $query->available();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $stock = $query->paginate($perPage);

        // Add calculated fields
        $stock->getCollection()->transform(function ($item) {
            $item->available_weight = $item->available_weight;
            $item->is_low_stock = $item->isLowStock();
            $item->is_out_of_stock = $item->isOutOfStock();
            return $item;
        });

        return response()->json([
            'success' => true,
            'data' => $stock,
            'message' => 'Stock retrieved successfully'
        ]);
    }

    /**
     * Display the specified stock
     */
    public function show(Stock $stock)
    {
        $stock->load([
            'product', 
            'reception.user', 
            'movements' => function($query) {
                $query->with('user')->orderBy('movement_date', 'desc');
            }
        ]);

        $stock->available_weight = $stock->available_weight;
        $stock->is_low_stock = $stock->isLowStock();
        $stock->is_out_of_stock = $stock->isOutOfStock();

        return response()->json([
            'success' => true,
            'data' => $stock,
            'message' => 'Stock details retrieved successfully'
        ]);
    }

    /**
     * Get low stock alerts
     */
    public function lowStock(Request $request)
    {
        $stock = Stock::lowStock()
            ->with(['product', 'reception'])
            ->available()
            ->get();

        $stock->transform(function ($item) {
            $item->available_weight = $item->available_weight;
            $item->days_since_reception = $item->reception->reception_date->diffInDays(now());
            return $item;
        });

        return response()->json([
            'success' => true,
            'data' => $stock,
            'message' => 'Low stock alerts retrieved successfully'
        ]);
    }

    /**
     * Get stock movements
     */
    public function movements(Request $request)
    {
        $query = StockMovement::with(['stock.product', 'user']);

        // Apply filters
        if ($request->has('stock_id') && $request->stock_id) {
            $query->byStock($request->stock_id);
        }

        if ($request->has('movement_type') && $request->movement_type) {
            $query->byMovementType($request->movement_type);
        }

        if ($request->has('user_id') && $request->user_id) {
            $query->byUser($request->user_id);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Default to recent movements
        if (!$request->has('start_date')) {
            $days = $request->get('days', 30);
            $query->recent($days);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'movement_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 20);
        $movements = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $movements,
            'message' => 'Stock movements retrieved successfully'
        ]);
    }

    /**
     * Get movements for specific stock
     */
    public function stockMovements(Stock $stock, Request $request)
    {
        $query = $stock->movements()->with('user');

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        $movements = $query->orderBy('movement_date', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $movements,
            'message' => 'Stock movements retrieved successfully'
        ]);
    }

    /**
     * Make stock adjustment
     */
    public function adjustment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'stock_id' => 'required|exists:stock,id',
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity_kg' => 'required|numeric|min:0.001',
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $stock = Stock::find($request->stock_id);
        
        if (!$stock->is_available) {
            return response()->json([
                'success' => false,
                'message' => 'Stock is not available for adjustment'
            ], 400);
        }

        $quantity = $request->quantity_kg;
        if ($request->adjustment_type === 'decrease') {
            if ($stock->current_weight_kg < $quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock for decrease adjustment'
                ], 400);
            }
            $quantity = -$quantity;
        }

        try {
            $oldWeight = $stock->current_weight_kg;
            $stock->current_weight_kg += $quantity;
            $stock->save();

            // Log the adjustment
            StockMovement::create([
                'stock_id' => $stock->id,
                'user_id' => auth()->id(),
                'movement_type' => 'adjustment',
                'quantity_kg' => $quantity,
                'balance_after' => $stock->current_weight_kg,
                'reason' => $request->reason,
                'movement_date' => now(),
            ]);

            // Log activity
            ActivityLog::logActivity(
                'updated',
                "Stock adjustment: {$request->adjustment_type} {$request->quantity_kg}kg for {$stock->product->name}",
                'Stock',
                $stock->id,
                ['current_weight_kg' => $oldWeight],
                ['current_weight_kg' => $stock->current_weight_kg]
            );

            $stock->load(['product', 'reception']);
            $stock->available_weight = $stock->available_weight;

            return response()->json([
                'success' => true,
                'data' => $stock,
                'message' => 'Stock adjustment completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to adjust stock: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update stock alert threshold
     */
    public function updateAlert(Request $request, Stock $stock)
    {
        $validator = Validator::make($request->all(), [
            'minimum_alert_kg' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $oldAlert = $stock->minimum_alert_kg;
        $stock->minimum_alert_kg = $request->minimum_alert_kg;
        $stock->save();

        // Log activity
        ActivityLog::logActivity(
            'updated',
            "Updated alert threshold for {$stock->product->name} from {$oldAlert}kg to {$request->minimum_alert_kg}kg",
            'Stock',
            $stock->id,
            ['minimum_alert_kg' => $oldAlert],
            ['minimum_alert_kg' => $request->minimum_alert_kg]
        );

        return response()->json([
            'success' => true,
            'data' => $stock,
            'message' => 'Alert threshold updated successfully'
        ]);
    }

    /**
     * Get stock statistics
     */
    public function statistics(Request $request)
    {
        $stats = [
            'total_products' => Product::active()->count(),
            'total_stock_value' => Stock::available()
                ->join('products', 'stock.product_id', '=', 'products.id')
                ->selectRaw('SUM(stock.current_weight_kg * products.unit_price) as total_value')
                ->value('total_value') ?? 0,
            'total_weight' => Stock::available()->sum('current_weight_kg'),
            'low_stock_count' => Stock::lowStock()->available()->count(),
            'out_of_stock_count' => Stock::outOfStock()->count(),
            'by_product' => Stock::available()
                ->with('product')
                ->get()
                ->groupBy('product.name')
                ->map(function ($stocks) {
                    $totalWeight = $stocks->sum('current_weight_kg');
                    $totalReserved = $stocks->sum('reserved_weight_kg');
                    $product = $stocks->first()->product;
                    
                    return [
                        'total_weight' => $totalWeight,
                        'available_weight' => $totalWeight - $totalReserved,
                        'reserved_weight' => $totalReserved,
                        'value' => $totalWeight * $product->unit_price,
                        'low_stock_count' => $stocks->filter(function($stock) {
                            return $stock->isLowStock();
                        })->count(),
                    ];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Stock statistics retrieved successfully'
        ]);
    }

    /**
     * Get stock valuation report
     */
    public function valuation(Request $request)
    {
        $stock = Stock::available()
            ->with(['product', 'reception'])
            ->get()
            ->map(function ($item) {
                $availableWeight = $item->available_weight;
                $value = $availableWeight * $item->product->unit_price;
                
                return [
                    'id' => $item->id,
                    'product_name' => $item->product->name,
                    'lot_number' => $item->reception->lot_number,
                    'reception_date' => $item->reception->reception_date,
                    'current_weight_kg' => $item->current_weight_kg,
                    'reserved_weight_kg' => $item->reserved_weight_kg,
                    'available_weight_kg' => $availableWeight,
                    'unit_price' => $item->product->unit_price,
                    'total_value' => $value,
                    'days_in_stock' => $item->reception->reception_date->diffInDays(now()),
                ];
            });

        $totalValue = $stock->sum('total_value');
        $totalWeight = $stock->sum('available_weight_kg');

        return response()->json([
            'success' => true,
            'data' => [
                'stock_items' => $stock,
                'summary' => [
                    'total_items' => $stock->count(),
                    'total_weight_kg' => $totalWeight,
                    'total_value' => $totalValue,
                    'average_value_per_kg' => $totalWeight > 0 ? $totalValue / $totalWeight : 0,
                ]
            ],
            'message' => 'Stock valuation retrieved successfully'
        ]);
    }
}
