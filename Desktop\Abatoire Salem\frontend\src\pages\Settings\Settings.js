import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { SettingOutlined, ToolOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Settings = () => {
  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> Paramètres
      </Title>
      
      <Card>
        <Result
          icon={<ToolOutlined />}
          title="Module en Développement"
          subTitle="Le module de paramètres est en cours de développement. Il comprendra la configuration générale du système."
          extra={
            <div>
              <p><strong>Fonctionnalités prévues :</strong></p>
              <ul style={{ textAlign: 'left', display: 'inline-block' }}>
                <li>Configuration de l'entreprise</li>
                <li>Paramètres des alertes stock</li>
                <li>Configuration des limites de crédit</li>
                <li>Gestion des produits et catégories</li>
                <li>Paramètres d'impression</li>
                <li>Sauvegarde et restauration</li>
              </ul>
              <Button type="primary" onClick={() => window.history.back()}>
                Retour
              </Button>
            </div>
          }
        />
      </Card>
    </div>
  );
};

export default Settings;
