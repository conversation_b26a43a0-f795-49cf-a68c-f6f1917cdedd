<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Poulet vidé", "Mouton entier"
            $table->string('animal_type'); // e.g., "Poulet", "Mouton", "Dinde"
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2); // Price per kg
            $table->decimal('average_weight', 8, 3)->nullable(); // Average weight per piece
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
