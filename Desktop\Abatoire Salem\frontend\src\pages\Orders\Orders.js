import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Tabs,
  Divider,
  Alert,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FilePdfOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  UserOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { ordersAPI, customersAPI, productsAPI } from '../../services/api';
import { useNotifications } from '../../contexts/NotificationContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const Orders = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [activeTab, setActiveTab] = useState('orders');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState(null);
  const [orderItems, setOrderItems] = useState([{ product_id: null, quantity_kg: 0, unit_price: 0 }]);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { notifyOrderStatusChange } = useNotifications();

  // Fetch orders
  const { data: orders, isLoading } = useQuery(
    ['orders', { searchText, selectedCustomer, selectedStatus, selectedPaymentStatus, dateRange }],
    () => ordersAPI.getOrders({
      search: searchText,
      customer_id: selectedCustomer,
      status: selectedStatus,
      payment_status: selectedPaymentStatus,
      start_date: dateRange?.[0]?.format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD'),
    }),
    {
      keepPreviousData: true,
    }
  );

  // Fetch customers for filter
  const { data: customers } = useQuery('customers', customersAPI.getCustomers);

  // Fetch products for order creation
  const { data: products } = useQuery('products', productsAPI.getProducts);

  // Fetch preparations
  const { data: preparations } = useQuery(
    ['preparations', activeTab],
    () => ordersAPI.getPreparations(),
    {
      enabled: activeTab === 'preparations',
    }
  );

  // Fetch statistics
  const { data: stats } = useQuery(
    ['orderStats', dateRange],
    () => ordersAPI.getStatistics({
      start_date: dateRange?.[0]?.format('YYYY-MM-DD') || dayjs().startOf('month').format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD') || dayjs().endOf('month').format('YYYY-MM-DD'),
    })
  );

  // Create order mutation
  const createMutation = useMutation(ordersAPI.createOrder, {
    onSuccess: (response) => {
      message.success('Commande créée avec succès');
      setModalVisible(false);
      form.resetFields();
      setOrderItems([{ product_id: null, quantity_kg: 0, unit_price: 0 }]);
      queryClient.invalidateQueries('orders');
      queryClient.invalidateQueries('orderStats');

      // Navigate to order details
      navigate(`/orders/${response.data.data.id}`);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de la création');
    },
  });

  // Update order mutation
  const updateMutation = useMutation(
    ({ id, data }) => ordersAPI.updateOrder(id, data),
    {
      onSuccess: () => {
        message.success('Commande mise à jour avec succès');
        setModalVisible(false);
        setEditingOrder(null);
        form.resetFields();
        setOrderItems([{ product_id: null, quantity_kg: 0, unit_price: 0 }]);
        queryClient.invalidateQueries('orders');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Erreur lors de la mise à jour');
      },
    }
  );

  // Confirm order mutation
  const confirmMutation = useMutation(ordersAPI.confirmOrder, {
    onSuccess: (response, orderId) => {
      message.success('Commande confirmée avec succès');
      queryClient.invalidateQueries('orders');

      const order = orders?.data?.data?.data?.find(o => o.id === orderId);
      if (order) {
        notifyOrderStatusChange(order, 'pending', 'confirmed');
      }
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de la confirmation');
    },
  });

  // Cancel order mutation
  const cancelMutation = useMutation(
    ({ id, reason }) => ordersAPI.cancelOrder(id, { reason }),
    {
      onSuccess: (response, { id }) => {
        message.success('Commande annulée avec succès');
        queryClient.invalidateQueries('orders');

        const order = orders?.data?.data?.data?.find(o => o.id === id);
        if (order) {
          notifyOrderStatusChange(order, order.status, 'cancelled');
        }
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Erreur lors de l\'annulation');
      },
    }
  );

  // Start preparation mutation
  const startPreparationMutation = useMutation(ordersAPI.startPreparation, {
    onSuccess: (response, orderId) => {
      message.success('Préparation démarrée avec succès');
      queryClient.invalidateQueries('orders');
      queryClient.invalidateQueries('preparations');

      const order = orders?.data?.data?.data?.find(o => o.id === orderId);
      if (order) {
        notifyOrderStatusChange(order, order.status, 'preparing');
      }
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors du démarrage de la préparation');
    },
  });

  const getStatusConfig = (status) => {
    const configs = {
      pending: { color: 'orange', icon: <ClockCircleOutlined />, text: 'En attente' },
      confirmed: { color: 'blue', icon: <CheckCircleOutlined />, text: 'Confirmée' },
      preparing: { color: 'purple', icon: <ClockCircleOutlined />, text: 'En préparation' },
      prepared: { color: 'green', icon: <CheckCircleOutlined />, text: 'Préparée' },
      delivered: { color: 'success', icon: <TruckOutlined />, text: 'Livrée' },
      cancelled: { color: 'red', icon: <CloseCircleOutlined />, text: 'Annulée' },
    };
    return configs[status] || { color: 'default', icon: null, text: status };
  };

  const getPaymentStatusConfig = (status) => {
    const configs = {
      unpaid: { color: 'red', text: 'Impayé' },
      partial: { color: 'orange', text: 'Partiel' },
      paid: { color: 'green', text: 'Payé' },
    };
    return configs[status] || { color: 'default', text: status };
  };

  const canConfirmOrder = (order) => {
    return order.status === 'pending';
  };

  const canCancelOrder = (order) => {
    return ['pending', 'confirmed'].includes(order.status);
  };

  const canStartPreparation = (order) => {
    return order.status === 'confirmed';
  };

  const calculateOrderTotal = () => {
    return orderItems.reduce((total, item) => {
      return total + (item.quantity_kg * item.unit_price);
    }, 0);
  };

  const handleAddOrderItem = () => {
    setOrderItems([...orderItems, { product_id: null, quantity_kg: 0, unit_price: 0 }]);
  };

  const handleRemoveOrderItem = (index) => {
    if (orderItems.length > 1) {
      const newItems = orderItems.filter((_, i) => i !== index);
      setOrderItems(newItems);
    }
  };

  const handleOrderItemChange = (index, field, value) => {
    const newItems = [...orderItems];
    newItems[index][field] = value;

    // Auto-fill unit price when product is selected
    if (field === 'product_id' && value) {
      const product = products?.data?.data?.find(p => p.id === value);
      if (product) {
        newItems[index].unit_price = product.unit_price;
      }
    }

    setOrderItems(newItems);
  };

  const handleEdit = (order) => {
    setEditingOrder(order);
    form.setFieldsValue({
      customer_id: order.customer_id,
      delivery_date: order.delivery_date ? dayjs(order.delivery_date) : null,
      payment_method: order.payment_method,
      notes: order.notes,
    });
    setOrderItems(order.order_items?.map(item => ({
      product_id: item.product_id,
      quantity_kg: item.quantity_kg,
      unit_price: item.unit_price,
    })) || [{ product_id: null, quantity_kg: 0, unit_price: 0 }]);
    setModalVisible(true);
  };

  const handleConfirm = (orderId) => {
    Modal.confirm({
      title: 'Confirmer la commande',
      content: 'Êtes-vous sûr de vouloir confirmer cette commande ? Cette action réservera le stock.',
      okText: 'Confirmer',
      cancelText: 'Annuler',
      onOk: () => confirmMutation.mutate(orderId),
    });
  };

  const handleCancel = (orderId) => {
    Modal.confirm({
      title: 'Annuler la commande',
      content: (
        <div>
          <p>Êtes-vous sûr de vouloir annuler cette commande ?</p>
          <Input.TextArea
            placeholder="Raison de l'annulation..."
            id="cancel-reason"
            rows={3}
          />
        </div>
      ),
      okText: 'Annuler la commande',
      cancelText: 'Retour',
      okType: 'danger',
      onOk: () => {
        const reason = document.getElementById('cancel-reason')?.value || 'Annulation sans raison';
        cancelMutation.mutate({ id: orderId, reason });
      },
    });
  };

  const handleStartPreparation = (orderId) => {
    Modal.confirm({
      title: 'Démarrer la préparation',
      content: 'Êtes-vous sûr de vouloir démarrer la préparation de cette commande ?',
      okText: 'Démarrer',
      cancelText: 'Annuler',
      onOk: () => startPreparationMutation.mutate(orderId),
    });
  };

  const handleDownloadPDF = async (orderId) => {
    try {
      const response = await ordersAPI.downloadPDF(orderId);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `commande_${orderId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      message.error('Erreur lors du téléchargement du PDF');
    }
  };

  const handleSubmit = (values) => {
    const data = {
      ...values,
      delivery_date: values.delivery_date?.format('YYYY-MM-DD'),
      order_items: orderItems.filter(item => item.product_id && item.quantity_kg > 0),
    };

    if (data.order_items.length === 0) {
      message.error('Veuillez ajouter au moins un produit à la commande');
      return;
    }

    if (editingOrder) {
      updateMutation.mutate({ id: editingOrder.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const ordersColumns = [
    {
      title: 'N° Commande',
      dataIndex: 'order_number',
      key: 'order_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Client',
      dataIndex: ['customer', 'name'],
      key: 'customer',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.customer?.phone}
          </Text>
        </div>
      ),
    },
    {
      title: 'Date Commande',
      dataIndex: 'order_date',
      key: 'order_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Montant Total',
      dataIndex: 'total_amount',
      key: 'total_amount',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(2)} DA`,
    },
    {
      title: 'Statut',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const config = getStatusConfig(status);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Paiement',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (status, record) => {
        const config = getPaymentStatusConfig(status);
        return (
          <div>
            <Tag color={config.color}>{config.text}</Tag>
            <br />
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {record.paid_amount}/{record.total_amount} DA
            </Text>
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Voir détails">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/orders/${record.id}`)}
            />
          </Tooltip>

          {canConfirmOrder(record) && (
            <Tooltip title="Confirmer">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleConfirm(record.id)}
                loading={confirmMutation.isLoading}
              />
            </Tooltip>
          )}

          {canStartPreparation(record) && (
            <Tooltip title="Démarrer préparation">
              <Button
                type="text"
                icon={<ClockCircleOutlined />}
                onClick={() => handleStartPreparation(record.id)}
                loading={startPreparationMutation.isLoading}
              />
            </Tooltip>
          )}

          <Tooltip title="Télécharger PDF">
            <Button
              type="text"
              icon={<FilePdfOutlined />}
              onClick={() => handleDownloadPDF(record.id)}
            />
          </Tooltip>

          {record.status === 'pending' && (
            <Tooltip title="Modifier">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          )}

          {canCancelOrder(record) && (
            <Tooltip title="Annuler">
              <Button
                type="text"
                danger
                icon={<CloseCircleOutlined />}
                onClick={() => handleCancel(record.id)}
                loading={cancelMutation.isLoading}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const preparationsColumns = [
    {
      title: 'N° Préparation',
      dataIndex: 'slip_number',
      key: 'slip_number',
      render: (text) => <Tag color="purple">{text}</Tag>,
    },
    {
      title: 'N° Commande',
      dataIndex: ['order', 'order_number'],
      key: 'order_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Client',
      dataIndex: ['order', 'customer', 'name'],
      key: 'customer',
    },
    {
      title: 'Statut',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const configs = {
          pending: { color: 'orange', text: 'En attente' },
          in_progress: { color: 'blue', text: 'En cours' },
          completed: { color: 'green', text: 'Terminée' },
        };
        const config = configs[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: 'Créé le',
      dataIndex: 'created_date',
      key: 'created_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: 'Préparé par',
      dataIndex: ['prepared_by', 'name'],
      key: 'prepared_by',
      render: (text) => text || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/orders/preparation/${record.id}`)}
          />
          <Button
            type="text"
            icon={<FilePdfOutlined />}
            onClick={() => handleDownloadPreparationPDF(record.id)}
          />
        </Space>
      ),
    },
  ];

  const handleDownloadPreparationPDF = async (preparationId) => {
    try {
      const response = await ordersAPI.downloadPreparationPDF(preparationId);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `preparation_${preparationId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      message.error('Erreur lors du téléchargement du PDF');
    }
  };

  const statsData = stats?.data?.data || {};

  return (
    <div>
      <Title level={2}>
        <ShoppingCartOutlined /> Gestion des Commandes
      </Title>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Commandes Totales"
              value={statsData.total_orders || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Chiffre d'Affaires"
              value={statsData.total_amount || 0}
              suffix="DA"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="En Attente"
              value={statsData.pending_orders || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Livrées"
              value={statsData.delivered_orders || 0}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <Input
              placeholder="Rechercher par N° commande..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={5}>
            <Select
              placeholder="Filtrer par client"
              allowClear
              value={selectedCustomer}
              onChange={setSelectedCustomer}
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {customers?.data?.data?.data?.map((customer) => (
                <Option key={customer.id} value={customer.id}>
                  {customer.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Statut"
              allowClear
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
            >
              <Option value="pending">En attente</Option>
              <Option value="confirmed">Confirmée</Option>
              <Option value="preparing">En préparation</Option>
              <Option value="prepared">Préparée</Option>
              <Option value="delivered">Livrée</Option>
              <Option value="cancelled">Annulée</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Paiement"
              allowClear
              value={selectedPaymentStatus}
              onChange={setSelectedPaymentStatus}
              style={{ width: '100%' }}
            >
              <Option value="unpaid">Impayé</Option>
              <Option value="partial">Partiel</Option>
              <Option value="paid">Payé</Option>
            </Select>
          </Col>
          <Col xs={24} sm={5}>
            <Space>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '200px' }}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                Nouvelle Commande
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Tabs */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <ShoppingCartOutlined />
                Commandes
                {orders?.data?.data?.total && (
                  <Badge count={orders.data.data.total} style={{ marginLeft: '8px' }} />
                )}
              </span>
            }
            key="orders"
          >
            <Table
              columns={ordersColumns}
              dataSource={orders?.data?.data?.data || []}
              loading={isLoading}
              rowKey="id"
              pagination={{
                current: orders?.data?.data?.current_page,
                total: orders?.data?.data?.total,
                pageSize: orders?.data?.data?.per_page,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} sur ${total} commandes`,
              }}
              scroll={{ x: 1200 }}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                Préparations
                {preparations?.data?.data?.length && (
                  <Badge count={preparations.data.data.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            }
            key="preparations"
          >
            <Table
              columns={preparationsColumns}
              dataSource={preparations?.data?.data || []}
              loading={isLoading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} sur ${total} préparations`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Order Modal */}
      <Modal
        title={editingOrder ? 'Modifier la Commande' : 'Nouvelle Commande'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
          setOrderItems([{ product_id: null, quantity_kg: 0, unit_price: 0 }]);
        }}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="customer_id"
                label="Client"
                rules={[{ required: true, message: 'Sélectionnez un client' }]}
              >
                <Select
                  placeholder="Sélectionnez un client"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {customers?.data?.data?.data?.map((customer) => (
                    <Option key={customer.id} value={customer.id}>
                      <div>
                        <Text strong>{customer.name}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {customer.phone} - Crédit: {customer.credit_limit} DA
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={6}>
              <Form.Item
                name="delivery_date"
                label="Date de Livraison"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={6}>
              <Form.Item
                name="payment_method"
                label="Mode de Paiement"
                rules={[{ required: true, message: 'Sélectionnez le mode de paiement' }]}
              >
                <Select placeholder="Mode de paiement">
                  <Option value="cash">Espèces</Option>
                  <Option value="credit">Crédit</Option>
                  <Option value="check">Chèque</Option>
                  <Option value="transfer">Virement</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>Articles de la Commande</Divider>

          {orderItems.map((item, index) => (
            <Row key={index} gutter={[16, 16]} style={{ marginBottom: '16px' }}>
              <Col xs={24} sm={8}>
                <Form.Item
                  label={index === 0 ? 'Produit' : ''}
                  required
                >
                  <Select
                    placeholder="Sélectionnez un produit"
                    value={item.product_id}
                    onChange={(value) => handleOrderItemChange(index, 'product_id', value)}
                    showSearch
                    optionFilterProp="children"
                  >
                    {products?.data?.data?.map((product) => (
                      <Option key={product.id} value={product.id}>
                        {product.name} ({product.animal_type})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} sm={5}>
                <Form.Item
                  label={index === 0 ? 'Quantité (kg)' : ''}
                  required
                >
                  <InputNumber
                    min={0.001}
                    precision={3}
                    style={{ width: '100%' }}
                    placeholder="Quantité"
                    value={item.quantity_kg}
                    onChange={(value) => handleOrderItemChange(index, 'quantity_kg', value)}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={5}>
                <Form.Item
                  label={index === 0 ? 'Prix Unitaire (DA)' : ''}
                  required
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="Prix"
                    value={item.unit_price}
                    onChange={(value) => handleOrderItemChange(index, 'unit_price', value)}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={4}>
                <Form.Item
                  label={index === 0 ? 'Total (DA)' : ''}
                >
                  <Text strong>
                    {(item.quantity_kg * item.unit_price).toFixed(2)} DA
                  </Text>
                </Form.Item>
              </Col>

              <Col xs={24} sm={2}>
                <Form.Item
                  label={index === 0 ? 'Action' : ''}
                >
                  <Space>
                    {index === orderItems.length - 1 && (
                      <Button
                        type="dashed"
                        icon={<PlusOutlined />}
                        onClick={handleAddOrderItem}
                        size="small"
                      />
                    )}
                    {orderItems.length > 1 && (
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleRemoveOrderItem(index)}
                        size="small"
                      />
                    )}
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          ))}

          <Row>
            <Col span={24}>
              <Alert
                message={`Total de la commande: ${calculateOrderTotal().toFixed(2)} DA`}
                type="info"
                style={{ marginBottom: '16px' }}
              />
            </Col>
          </Row>

          <Row>
            <Col span={24}>
              <Form.Item
                name="notes"
                label="Notes"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Notes additionnelles..."
                />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: '16px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Annuler
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isLoading || updateMutation.isLoading}
                disabled={orderItems.length === 0 || !orderItems.some(item => item.product_id && item.quantity_kg > 0)}
              >
                {editingOrder ? 'Modifier' : 'Créer'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default Orders;
