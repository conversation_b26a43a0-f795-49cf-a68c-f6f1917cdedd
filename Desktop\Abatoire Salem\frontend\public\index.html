<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2c3e50" />
    <meta
      name="description"
      content="Système de Gestion Intégré pour Abattoir Salem - Gestion complète des réceptions, stock, commandes, livraisons et traçabilité"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>Abattoir Salem - Système de Gestion</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      }
      
      #loading-screen .logo {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
      }
      
      #loading-screen .subtitle {
        font-size: 1.1rem;
        font-weight: 300;
        margin-bottom: 2rem;
        opacity: 0.8;
        text-align: center;
      }
      
      .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 1rem;
        font-size: 0.9rem;
        opacity: 0.7;
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        display: none;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h2>JavaScript requis</h2>
        <p>Vous devez activer JavaScript pour utiliser cette application.</p>
        <p>Veuillez activer JavaScript dans votre navigateur et recharger la page.</p>
      </div>
    </noscript>
    
    <!-- Loading screen -->
    <div id="loading-screen">
      <div class="logo">Abattoir Salem</div>
      <div class="subtitle">Système de Gestion Intégré</div>
      <div class="spinner"></div>
      <div class="loading-text">Chargement en cours...</div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
      
      // Fallback: hide loading screen after 10 seconds
      setTimeout(function() {
        document.body.classList.add('app-loaded');
      }, 10000);
    </script>
  </body>
</html>
