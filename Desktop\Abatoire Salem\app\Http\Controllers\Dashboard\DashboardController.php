<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Stock;
use App\Models\Reception;
use App\Models\Payment;
use App\Models\DeliveryNote;
use App\Models\PreparationSlip;
use App\Models\ActivityLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Get general dashboard statistics
     */
    public function stats(Request $request)
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        $stats = [
            // Today's stats
            'today' => [
                'orders' => Order::whereDate('order_date', $today)->count(),
                'sales_amount' => Order::whereDate('order_date', $today)->sum('total_amount'),
                'payments' => Payment::whereDate('payment_date', $today)->sum('amount'),
                'deliveries' => DeliveryNote::whereDate('delivery_date', $today)->count(),
                'receptions' => Reception::whereDate('reception_date', $today)->count(),
                'reception_weight' => Reception::whereDate('reception_date', $today)->sum('total_weight_kg'),
            ],

            // This month's stats
            'this_month' => [
                'orders' => Order::where('order_date', '>=', $thisMonth)->count(),
                'sales_amount' => Order::where('order_date', '>=', $thisMonth)->sum('total_amount'),
                'payments' => Payment::where('payment_date', '>=', $thisMonth)->sum('amount'),
                'deliveries' => DeliveryNote::where('delivery_date', '>=', $thisMonth)->count(),
                'receptions' => Reception::where('reception_date', '>=', $thisMonth)->count(),
                'reception_weight' => Reception::where('reception_date', '>=', $thisMonth)->sum('total_weight_kg'),
            ],

            // General stats
            'general' => [
                'total_customers' => Customer::active()->count(),
                'total_products' => Product::active()->count(),
                'low_stock_alerts' => Stock::lowStock()->available()->count(),
                'out_of_stock' => Stock::outOfStock()->count(),
                'pending_orders' => Order::where('status', 'pending')->count(),
                'unpaid_orders' => Order::whereIn('payment_status', ['unpaid', 'partial'])->count(),
                'total_stock_value' => $this->calculateStockValue(),
                'customers_with_credit' => Customer::withUnpaidOrders()->count(),
            ],

            // Order status breakdown
            'order_status' => Order::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get()
                ->keyBy('status'),

            // Payment status breakdown
            'payment_status' => Order::selectRaw('payment_status, COUNT(*) as count, SUM(total_amount) as total_amount')
                ->groupBy('payment_status')
                ->get()
                ->keyBy('payment_status'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Dashboard statistics retrieved successfully'
        ]);
    }

    /**
     * Get daily statistics for charts
     */
    public function dailyStats(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        // Generate date range
        $dateRange = [];
        $current = $startDate->copy();
        while ($current <= $endDate) {
            $dateRange[] = $current->format('Y-m-d');
            $current->addDay();
        }

        // Get daily orders
        $dailyOrders = Order::selectRaw('DATE(order_date) as date, COUNT(*) as count, SUM(total_amount) as amount')
            ->whereBetween('order_date', [$startDate, $endDate])
            ->groupBy('date')
            ->get()
            ->keyBy('date');

        // Get daily payments
        $dailyPayments = Payment::selectRaw('DATE(payment_date) as date, COUNT(*) as count, SUM(amount) as amount')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->groupBy('date')
            ->get()
            ->keyBy('date');

        // Get daily receptions
        $dailyReceptions = Reception::selectRaw('DATE(reception_date) as date, COUNT(*) as count, SUM(total_weight_kg) as weight')
            ->whereBetween('reception_date', [$startDate, $endDate])
            ->groupBy('date')
            ->get()
            ->keyBy('date');

        // Format data for charts
        $chartData = [
            'dates' => $dateRange,
            'orders' => [
                'count' => array_map(fn($date) => $dailyOrders[$date]->count ?? 0, $dateRange),
                'amount' => array_map(fn($date) => $dailyOrders[$date]->amount ?? 0, $dateRange),
            ],
            'payments' => [
                'count' => array_map(fn($date) => $dailyPayments[$date]->count ?? 0, $dateRange),
                'amount' => array_map(fn($date) => $dailyPayments[$date]->amount ?? 0, $dateRange),
            ],
            'receptions' => [
                'count' => array_map(fn($date) => $dailyReceptions[$date]->count ?? 0, $dateRange),
                'weight' => array_map(fn($date) => $dailyReceptions[$date]->weight ?? 0, $dateRange),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $chartData,
            'message' => 'Daily statistics retrieved successfully'
        ]);
    }

    /**
     * Get monthly statistics
     */
    public function monthlyStats(Request $request)
    {
        $months = $request->get('months', 12);
        $startDate = Carbon::now()->subMonths($months)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // Get monthly data
        $monthlyData = Order::selectRaw('
                YEAR(order_date) as year, 
                MONTH(order_date) as month,
                COUNT(*) as orders_count,
                SUM(total_amount) as total_amount,
                SUM(paid_amount) as paid_amount
            ')
            ->whereBetween('order_date', [$startDate, $endDate])
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        // Format for charts
        $chartData = $monthlyData->map(function ($item) {
            return [
                'period' => Carbon::create($item->year, $item->month, 1)->format('M Y'),
                'orders_count' => $item->orders_count,
                'total_amount' => $item->total_amount,
                'paid_amount' => $item->paid_amount,
                'unpaid_amount' => $item->total_amount - $item->paid_amount,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $chartData,
            'message' => 'Monthly statistics retrieved successfully'
        ]);
    }

    /**
     * Get top customers
     */
    public function topCustomers(Request $request)
    {
        $period = $request->get('period', 'month'); // month, quarter, year, all
        $limit = $request->get('limit', 10);

        $query = Order::with('customer')
            ->selectRaw('customer_id, COUNT(*) as orders_count, SUM(total_amount) as total_amount, SUM(paid_amount) as paid_amount')
            ->groupBy('customer_id');

        // Apply period filter
        switch ($period) {
            case 'month':
                $query->where('order_date', '>=', Carbon::now()->startOfMonth());
                break;
            case 'quarter':
                $query->where('order_date', '>=', Carbon::now()->startOfQuarter());
                break;
            case 'year':
                $query->where('order_date', '>=', Carbon::now()->startOfYear());
                break;
            // 'all' - no filter
        }

        $topCustomers = $query->orderBy('total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'customer' => $item->customer,
                    'orders_count' => $item->orders_count,
                    'total_amount' => $item->total_amount,
                    'paid_amount' => $item->paid_amount,
                    'unpaid_amount' => $item->total_amount - $item->paid_amount,
                    'average_order_value' => $item->orders_count > 0 ? $item->total_amount / $item->orders_count : 0,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $topCustomers,
            'message' => 'Top customers retrieved successfully'
        ]);
    }

    /**
     * Get low stock alerts
     */
    public function lowStockAlerts(Request $request)
    {
        $alerts = Stock::lowStock()
            ->available()
            ->with(['product', 'reception'])
            ->get()
            ->map(function ($stock) {
                return [
                    'id' => $stock->id,
                    'product' => $stock->product,
                    'lot_number' => $stock->reception->lot_number,
                    'current_weight_kg' => $stock->current_weight_kg,
                    'reserved_weight_kg' => $stock->reserved_weight_kg,
                    'available_weight_kg' => $stock->available_weight,
                    'minimum_alert_kg' => $stock->minimum_alert_kg,
                    'reception_date' => $stock->reception->reception_date,
                    'days_in_stock' => $stock->reception->reception_date->diffInDays(now()),
                    'urgency_level' => $this->getUrgencyLevel($stock),
                ];
            })
            ->sortBy('urgency_level')
            ->values();

        return response()->json([
            'success' => true,
            'data' => $alerts,
            'message' => 'Low stock alerts retrieved successfully'
        ]);
    }

    /**
     * Get recent activities
     */
    public function recentActivities(Request $request)
    {
        $limit = $request->get('limit', 20);
        $days = $request->get('days', 7);

        $activities = ActivityLog::with('user')
            ->where('created_at', '>=', Carbon::now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'user' => $activity->user ? $activity->user->name : 'System',
                    'action' => $activity->action,
                    'description' => $activity->description,
                    'model_type' => $activity->model_type,
                    'model_id' => $activity->model_id,
                    'created_at' => $activity->created_at,
                    'time_ago' => $activity->created_at->diffForHumans(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $activities,
            'message' => 'Recent activities retrieved successfully'
        ]);
    }

    /**
     * Calculate total stock value
     */
    private function calculateStockValue(): float
    {
        return Stock::available()
            ->join('products', 'stock.product_id', '=', 'products.id')
            ->selectRaw('SUM((stock.current_weight_kg - stock.reserved_weight_kg) * products.unit_price) as total_value')
            ->value('total_value') ?? 0;
    }

    /**
     * Get urgency level for stock alert
     */
    private function getUrgencyLevel(Stock $stock): int
    {
        $availableWeight = $stock->available_weight;
        $alertThreshold = $stock->minimum_alert_kg;

        if ($availableWeight <= 0) {
            return 1; // Critical - out of stock
        } elseif ($availableWeight <= $alertThreshold * 0.5) {
            return 2; // High - very low stock
        } elseif ($availableWeight <= $alertThreshold) {
            return 3; // Medium - low stock
        } else {
            return 4; // Low - approaching threshold
        }
    }
}
