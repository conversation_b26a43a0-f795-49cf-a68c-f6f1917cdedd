import React, { createContext, useContext, useState, useEffect } from 'react';
import { notification, Badge } from 'antd';
import { 
  BellOutlined, 
  WarningOutlined, 
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { dashboardAPI, stockAPI } from '../services/api';

// Create context
const NotificationContext = createContext();

// Provider component
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch low stock alerts
  const { data: lowStockAlerts } = useQuery(
    'lowStockAlerts',
    dashboardAPI.getLowStockAlerts,
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
      onSuccess: (response) => {
        if (response.data.success && response.data.data.length > 0) {
          handleLowStockAlerts(response.data.data);
        }
      },
    }
  );

  // Handle low stock alerts
  const handleLowStockAlerts = (alerts) => {
    alerts.forEach(alert => {
      const existingNotification = notifications.find(
        n => n.key === `low-stock-${alert.id}`
      );

      if (!existingNotification) {
        addNotification({
          key: `low-stock-${alert.id}`,
          type: 'warning',
          title: 'Stock Faible',
          message: `${alert.product.name} - Stock: ${alert.available_weight_kg}kg`,
          data: alert,
          category: 'stock',
          priority: alert.urgency_level,
        });
      }
    });
  };

  // Add notification
  const addNotification = (notificationData) => {
    const newNotification = {
      id: Date.now(),
      timestamp: new Date(),
      read: false,
      ...notificationData,
    };

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Show system notification
    showSystemNotification(newNotification);
  };

  // Show system notification
  const showSystemNotification = (notif) => {
    const icons = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      info: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      warning: <WarningOutlined style={{ color: '#faad14' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
    };

    notification[notif.type || 'info']({
      message: notif.title,
      description: notif.message,
      icon: icons[notif.type || 'info'],
      placement: 'topRight',
      duration: notif.type === 'error' ? 0 : 4.5,
    });
  };

  // Mark notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId
          ? { ...notif, read: true }
          : notif
      )
    );

    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
    setUnreadCount(0);
  };

  // Remove notification
  const removeNotification = (notificationId) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }

    setNotifications(prev =>
      prev.filter(notif => notif.id !== notificationId)
    );
  };

  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  // Get notifications by category
  const getNotificationsByCategory = (category) => {
    return notifications.filter(notif => notif.category === category);
  };

  // Get unread notifications
  const getUnreadNotifications = () => {
    return notifications.filter(notif => !notif.read);
  };

  // Show success notification
  const showSuccess = (title, message) => {
    addNotification({
      type: 'success',
      title,
      message,
      category: 'system',
    });
  };

  // Show error notification
  const showError = (title, message) => {
    addNotification({
      type: 'error',
      title,
      message,
      category: 'system',
    });
  };

  // Show warning notification
  const showWarning = (title, message) => {
    addNotification({
      type: 'warning',
      title,
      message,
      category: 'system',
    });
  };

  // Show info notification
  const showInfo = (title, message) => {
    addNotification({
      type: 'info',
      title,
      message,
      category: 'system',
    });
  };

  // Order status notifications
  const notifyOrderStatusChange = (order, oldStatus, newStatus) => {
    const statusMessages = {
      confirmed: 'Commande confirmée',
      preparing: 'Préparation démarrée',
      prepared: 'Préparation terminée',
      delivered: 'Commande livrée',
      cancelled: 'Commande annulée',
    };

    if (statusMessages[newStatus]) {
      addNotification({
        type: newStatus === 'cancelled' ? 'warning' : 'info',
        title: statusMessages[newStatus],
        message: `Commande ${order.order_number} - Client: ${order.customer.name}`,
        category: 'orders',
        data: order,
      });
    }
  };

  // Payment notifications
  const notifyPaymentReceived = (payment) => {
    addNotification({
      type: 'success',
      title: 'Paiement reçu',
      message: `${payment.amount} DA de ${payment.customer.name}`,
      category: 'payments',
      data: payment,
    });
  };

  // Delivery notifications
  const notifyDeliveryStatusChange = (delivery, status) => {
    const statusMessages = {
      in_transit: 'Livraison en cours',
      delivered: 'Livraison terminée',
      returned: 'Livraison retournée',
    };

    if (statusMessages[status]) {
      addNotification({
        type: status === 'returned' ? 'warning' : 'info',
        title: statusMessages[status],
        message: `Livraison ${delivery.delivery_number}`,
        category: 'deliveries',
        data: delivery,
      });
    }
  };

  // Stock notifications
  const notifyStockMovement = (movement) => {
    if (movement.movement_type === 'out' && movement.balance_after <= movement.stock.minimum_alert_kg) {
      addNotification({
        type: 'warning',
        title: 'Stock critique',
        message: `${movement.stock.product.name} - Reste: ${movement.balance_after}kg`,
        category: 'stock',
        data: movement,
      });
    }
  };

  const value = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    getNotificationsByCategory,
    getUnreadNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    notifyOrderStatusChange,
    notifyPaymentReceived,
    notifyDeliveryStatusChange,
    notifyStockMovement,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
