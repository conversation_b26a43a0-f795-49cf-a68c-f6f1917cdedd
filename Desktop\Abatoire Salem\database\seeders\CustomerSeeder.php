<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'name' => 'Restaurant El Bahdja',
                'phone' => '+213555100001',
                'email' => '<EMAIL>',
                'address' => 'Centre-ville, Alger',
                'credit_limit' => 50000.00,
                'max_unpaid_orders' => 3,
            ],
            [
                'name' => 'Boucherie Moderne',
                'phone' => '+213555100002',
                'email' => '<EMAIL>',
                'address' => 'Rue Didouche Mourad, Alger',
                'credit_limit' => 30000.00,
                'max_unpaid_orders' => 3,
            ],
            [
                'name' => 'Supermarché Familia',
                'phone' => '+213555100003',
                'email' => '<EMAIL>',
                'address' => '<PERSON><PERSON>, <PERSON><PERSON>',
                'credit_limit' => 75000.00,
                'max_unpaid_orders' => 5,
            ],
            [
                'name' => 'Restaurant Le Gourmet',
                'phone' => '+213555100004',
                'email' => '<EMAIL>',
                'address' => 'Hydra, Alger',
                'credit_limit' => 40000.00,
                'max_unpaid_orders' => 3,
            ],
            [
                'name' => 'Boucherie Traditionnelle',
                'phone' => '+213555100005',
                'email' => null,
                'address' => 'Casbah, Alger',
                'credit_limit' => 20000.00,
                'max_unpaid_orders' => 2,
            ],
            [
                'name' => 'Hôtel Aurassi',
                'phone' => '+213555100006',
                'email' => '<EMAIL>',
                'address' => 'Avenue Docteur Frantz Fanon, Alger',
                'credit_limit' => 100000.00,
                'max_unpaid_orders' => 5,
            ],
            [
                'name' => 'Restaurant Familial',
                'phone' => '+213555100007',
                'email' => '<EMAIL>',
                'address' => 'Kouba, Alger',
                'credit_limit' => 25000.00,
                'max_unpaid_orders' => 3,
            ],
            [
                'name' => 'Boucherie El Khair',
                'phone' => '+213555100008',
                'email' => null,
                'address' => 'Belcourt, Alger',
                'credit_limit' => 15000.00,
                'max_unpaid_orders' => 2,
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
