<?php

namespace App\Http\Controllers\Tracabilite;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Reception;
use App\Models\Stock;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use App\Models\Product;
use App\Models\StockMovement;
use App\Models\DeliveryNote;
use Carbon\Carbon;

class TraceabilityController extends Controller
{
    /**
     * General search across all traceability data
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2',
            'type' => 'nullable|in:lot,order,customer,product,all',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = $request->query;
        $type = $request->get('type', 'all');
        $results = [];

        try {
            if ($type === 'all' || $type === 'lot') {
                $results['lots'] = $this->searchLots($query);
            }

            if ($type === 'all' || $type === 'order') {
                $results['orders'] = $this->searchOrders($query);
            }

            if ($type === 'all' || $type === 'customer') {
                $results['customers'] = $this->searchCustomers($query);
            }

            if ($type === 'all' || $type === 'product') {
                $results['products'] = $this->searchProducts($query);
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Search completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get complete traceability by lot number
     */
    public function byLot(Request $request, string $lotNumber)
    {
        $reception = Reception::where('lot_number', 'like', "%{$lotNumber}%")
            ->with(['product', 'user', 'stock'])
            ->first();

        if (!$reception) {
            return response()->json([
                'success' => false,
                'message' => 'Lot not found'
            ], 404);
        }

        try {
            $traceability = [
                'lot_info' => [
                    'lot_number' => $reception->lot_number,
                    'product' => $reception->product,
                    'quantity_heads' => $reception->quantity_heads,
                    'total_weight_kg' => $reception->total_weight_kg,
                    'average_weight_kg' => $reception->average_weight_kg,
                    'origin' => $reception->origin,
                    'slaughter_date' => $reception->slaughter_date,
                    'reception_date' => $reception->reception_date,
                    'received_by' => $reception->user,
                    'notes' => $reception->notes,
                ],
                'stock_info' => $reception->stock ? [
                    'initial_weight_kg' => $reception->stock->initial_weight_kg,
                    'current_weight_kg' => $reception->stock->current_weight_kg,
                    'reserved_weight_kg' => $reception->stock->reserved_weight_kg,
                    'available_weight_kg' => $reception->stock->available_weight,
                    'is_available' => $reception->stock->is_available,
                ] : null,
                'stock_movements' => $this->getStockMovements($reception->stock),
                'orders' => $this->getOrdersByStock($reception->stock),
                'deliveries' => $this->getDeliveriesByStock($reception->stock),
                'timeline' => $this->buildLotTimeline($reception),
            ];

            return response()->json([
                'success' => true,
                'data' => $traceability,
                'message' => 'Lot traceability retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve lot traceability: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get traceability by product
     */
    public function byProduct(Request $request, Product $product)
    {
        $startDate = $request->get('start_date', Carbon::now()->subMonth());
        $endDate = $request->get('end_date', Carbon::now());

        try {
            $traceability = [
                'product_info' => $product,
                'receptions' => Reception::where('product_id', $product->id)
                    ->byDateRange($startDate, $endDate)
                    ->with(['user', 'stock'])
                    ->orderBy('reception_date', 'desc')
                    ->get(),
                'current_stock' => Stock::where('product_id', $product->id)
                    ->available()
                    ->with('reception')
                    ->get(),
                'recent_orders' => OrderItem::where('product_id', $product->id)
                    ->with(['order.customer', 'stock.reception'])
                    ->whereHas('order', function($query) use ($startDate, $endDate) {
                        $query->whereBetween('order_date', [$startDate, $endDate]);
                    })
                    ->orderBy('created_at', 'desc')
                    ->get(),
                'statistics' => [
                    'total_received_kg' => Reception::where('product_id', $product->id)
                        ->byDateRange($startDate, $endDate)
                        ->sum('total_weight_kg'),
                    'total_sold_kg' => OrderItem::where('product_id', $product->id)
                        ->whereHas('order', function($query) use ($startDate, $endDate) {
                            $query->whereBetween('order_date', [$startDate, $endDate])
                                  ->where('status', '!=', 'cancelled');
                        })
                        ->sum('quantity_kg'),
                    'current_stock_kg' => $product->total_stock,
                    'available_stock_kg' => $product->available_stock,
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $traceability,
                'message' => 'Product traceability retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product traceability: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get traceability by customer
     */
    public function byCustomer(Request $request, Customer $customer)
    {
        $startDate = $request->get('start_date', Carbon::now()->subMonth());
        $endDate = $request->get('end_date', Carbon::now());

        try {
            $orders = $customer->orders()
                ->with(['orderItems.product', 'orderItems.stock.reception', 'deliveryNote'])
                ->byDateRange($startDate, $endDate)
                ->orderBy('order_date', 'desc')
                ->get();

            $traceability = [
                'customer_info' => $customer,
                'orders' => $orders->map(function($order) {
                    return [
                        'order' => $order,
                        'items_traceability' => $order->orderItems->map(function($item) {
                            return [
                                'product' => $item->product,
                                'quantity_kg' => $item->quantity_kg,
                                'unit_price' => $item->unit_price,
                                'lot_info' => $item->stock ? [
                                    'lot_number' => $item->stock->reception->lot_number,
                                    'origin' => $item->stock->reception->origin,
                                    'slaughter_date' => $item->stock->reception->slaughter_date,
                                    'reception_date' => $item->stock->reception->reception_date,
                                ] : null,
                            ];
                        }),
                        'delivery_info' => $order->deliveryNote ? [
                            'delivery_number' => $order->deliveryNote->delivery_number,
                            'status' => $order->deliveryNote->status,
                            'delivery_date' => $order->deliveryNote->delivery_date,
                            'delivered_at' => $order->deliveryNote->delivered_at,
                            'delivered_by' => $order->deliveryNote->deliveredBy->name ?? null,
                        ] : null,
                    ];
                }),
                'statistics' => [
                    'total_orders' => $orders->count(),
                    'total_amount' => $orders->sum('total_amount'),
                    'total_paid' => $orders->sum('paid_amount'),
                    'total_weight_kg' => $orders->sum(function($order) {
                        return $order->orderItems->sum('quantity_kg');
                    }),
                    'products_purchased' => $orders->flatMap(function($order) {
                        return $order->orderItems->pluck('product.name');
                    })->unique()->values(),
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $traceability,
                'message' => 'Customer traceability retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer traceability: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get traceability by date range
     */
    public function byDateRange(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'type' => 'nullable|in:reception,order,delivery,all',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $type = $request->get('type', 'all');

        try {
            $data = [];

            if ($type === 'all' || $type === 'reception') {
                $data['receptions'] = Reception::byDateRange($startDate, $endDate)
                    ->with(['product', 'user', 'stock'])
                    ->orderBy('reception_date', 'desc')
                    ->get();
            }

            if ($type === 'all' || $type === 'order') {
                $data['orders'] = Order::byDateRange($startDate, $endDate)
                    ->with(['customer', 'orderItems.product', 'orderItems.stock.reception'])
                    ->orderBy('order_date', 'desc')
                    ->get();
            }

            if ($type === 'all' || $type === 'delivery') {
                $data['deliveries'] = DeliveryNote::whereBetween('delivery_date', [$startDate, $endDate])
                    ->with(['order.customer', 'order.orderItems.product', 'deliveredBy'])
                    ->orderBy('delivery_date', 'desc')
                    ->get();
            }

            // Summary statistics
            $data['summary'] = [
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'days' => Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate)) + 1,
                ],
                'totals' => [
                    'receptions' => Reception::byDateRange($startDate, $endDate)->count(),
                    'total_weight_received' => Reception::byDateRange($startDate, $endDate)->sum('total_weight_kg'),
                    'orders' => Order::byDateRange($startDate, $endDate)->count(),
                    'total_sales_amount' => Order::byDateRange($startDate, $endDate)->sum('total_amount'),
                    'deliveries' => DeliveryNote::whereBetween('delivery_date', [$startDate, $endDate])->count(),
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'Date range traceability retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve date range traceability: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search lots
     */
    private function searchLots(string $query): array
    {
        return Reception::where('lot_number', 'like', "%{$query}%")
            ->orWhere('origin', 'like', "%{$query}%")
            ->with(['product', 'user'])
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Search orders
     */
    private function searchOrders(string $query): array
    {
        return Order::where('order_number', 'like', "%{$query}%")
            ->with(['customer', 'user'])
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Search customers
     */
    private function searchCustomers(string $query): array
    {
        return Customer::where('name', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->orWhere('email', 'like', "%{$query}%")
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Search products
     */
    private function searchProducts(string $query): array
    {
        return Product::where('name', 'like', "%{$query}%")
            ->orWhere('animal_type', 'like', "%{$query}%")
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get stock movements for a stock
     */
    private function getStockMovements(?Stock $stock): array
    {
        if (!$stock) return [];

        return $stock->movements()
            ->with('user')
            ->orderBy('movement_date', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Get orders that used this stock
     */
    private function getOrdersByStock(?Stock $stock): array
    {
        if (!$stock) return [];

        return OrderItem::where('stock_id', $stock->id)
            ->with(['order.customer', 'product'])
            ->get()
            ->map(function($item) {
                return [
                    'order' => $item->order,
                    'product' => $item->product,
                    'quantity_kg' => $item->quantity_kg,
                    'unit_price' => $item->unit_price,
                ];
            })
            ->toArray();
    }

    /**
     * Get deliveries for orders that used this stock
     */
    private function getDeliveriesByStock(?Stock $stock): array
    {
        if (!$stock) return [];

        $orderIds = OrderItem::where('stock_id', $stock->id)->pluck('order_id');
        
        return DeliveryNote::whereIn('order_id', $orderIds)
            ->with(['order.customer', 'deliveredBy'])
            ->get()
            ->toArray();
    }

    /**
     * Build timeline for a lot
     */
    private function buildLotTimeline(Reception $reception): array
    {
        $timeline = [];

        // Reception event
        $timeline[] = [
            'date' => $reception->reception_date,
            'type' => 'reception',
            'description' => "Received {$reception->quantity_heads} {$reception->product->animal_type} ({$reception->total_weight_kg}kg)",
            'user' => $reception->user->name,
            'details' => [
                'lot_number' => $reception->lot_number,
                'origin' => $reception->origin,
                'slaughter_date' => $reception->slaughter_date,
            ],
        ];

        // Stock movements
        if ($reception->stock) {
            foreach ($reception->stock->movements as $movement) {
                $timeline[] = [
                    'date' => $movement->movement_date,
                    'type' => 'stock_movement',
                    'description' => $movement->reason,
                    'user' => $movement->user->name,
                    'details' => [
                        'movement_type' => $movement->movement_type,
                        'quantity_kg' => $movement->quantity_kg,
                        'balance_after' => $movement->balance_after,
                    ],
                ];
            }
        }

        // Sort by date
        usort($timeline, function($a, $b) {
            return $a['date'] <=> $b['date'];
        });

        return $timeline;
    }
}
