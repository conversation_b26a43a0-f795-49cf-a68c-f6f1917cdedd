import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Select,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  message,
  Tabs,
  Timeline,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
  Steps,
  Tree,
  Empty,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  FilePdfOutlined,
  BarChartOutlined,
  InboxOutlined,
  DatabaseOutlined,
  ShoppingCartOutlined,
  TruckOutlined,
  UserOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  AuditOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { traceabilityAPI, receptionAPI, productsAPI } from '../../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { Step } = Steps;

const Traceability = () => {
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('lot_number');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [activeTab, setActiveTab] = useState('search');
  const [selectedTrace, setSelectedTrace] = useState(null);
  const [traceDetailsVisible, setTraceDetailsVisible] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Fetch traceability data
  const { data: traceabilityData, isLoading, refetch } = useQuery(
    ['traceability', { searchText, searchType, selectedProduct, dateRange }],
    () => traceabilityAPI.search({
      search: searchText,
      search_type: searchType,
      product_id: selectedProduct,
      start_date: dateRange?.[0]?.format('YYYY-MM-DD'),
      end_date: dateRange?.[1]?.format('YYYY-MM-DD'),
    }),
    {
      enabled: false, // Only search when user clicks search button
    }
  );

  // Fetch products for filter
  const { data: products } = useQuery('products', productsAPI.getProducts);

  // Fetch statistics
  const { data: stats } = useQuery('traceabilityStats', traceabilityAPI.getStatistics);

  // Generate report mutation
  const generateReportMutation = useMutation(traceabilityAPI.generateReport, {
    onSuccess: (response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport_tracabilite_${dayjs().format('YYYY-MM-DD')}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      message.success('Rapport généré avec succès');
      setReportModalVisible(false);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Erreur lors de la génération du rapport');
    },
  });

  const handleSearch = () => {
    if (!searchText.trim() && !selectedProduct && !dateRange) {
      message.warning('Veuillez saisir au moins un critère de recherche');
      return;
    }
    refetch();
  };

  const handleViewDetails = (trace) => {
    setSelectedTrace(trace);
    setTraceDetailsVisible(true);
  };

  const handleGenerateReport = (values) => {
    generateReportMutation.mutate({
      ...values,
      start_date: values.date_range?.[0]?.format('YYYY-MM-DD'),
      end_date: values.date_range?.[1]?.format('YYYY-MM-DD'),
    });
  };

  const getTraceabilitySteps = (trace) => {
    const steps = [];

    // Reception
    if (trace.reception) {
      steps.push({
        title: 'Réception',
        description: `Lot ${trace.reception.lot_number}`,
        status: 'finish',
        icon: <InboxOutlined />,
        date: trace.reception.reception_date,
        details: {
          quantity: `${trace.reception.quantity_heads} têtes`,
          weight: `${trace.reception.total_weight_kg} kg`,
          origin: trace.reception.origin,
        },
      });
    }

    // Stock
    if (trace.stock_movements?.length > 0) {
      steps.push({
        title: 'Stock',
        description: `${trace.stock_movements.length} mouvement(s)`,
        status: 'finish',
        icon: <DatabaseOutlined />,
        date: trace.stock_movements[0]?.created_at,
        details: {
          current_stock: `${trace.current_stock_kg || 0} kg`,
          movements: trace.stock_movements.length,
        },
      });
    }

    // Orders
    if (trace.orders?.length > 0) {
      steps.push({
        title: 'Commandes',
        description: `${trace.orders.length} commande(s)`,
        status: 'finish',
        icon: <ShoppingCartOutlined />,
        date: trace.orders[0]?.order_date,
        details: {
          customers: [...new Set(trace.orders.map(o => o.customer?.name))].join(', '),
          total_amount: trace.orders.reduce((sum, o) => sum + o.total_amount, 0),
        },
      });
    }

    // Deliveries
    if (trace.deliveries?.length > 0) {
      const allDelivered = trace.deliveries.every(d => d.status === 'delivered');
      steps.push({
        title: 'Livraisons',
        description: `${trace.deliveries.length} livraison(s)`,
        status: allDelivered ? 'finish' : 'process',
        icon: <TruckOutlined />,
        date: trace.deliveries[0]?.delivered_at || trace.deliveries[0]?.created_at,
        details: {
          delivered: trace.deliveries.filter(d => d.status === 'delivered').length,
          total: trace.deliveries.length,
        },
      });
    }

    return steps;
  };

  const columns = [
    {
      title: 'N° Lot',
      dataIndex: ['reception', 'lot_number'],
      key: 'lot_number',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Produit',
      dataIndex: ['reception', 'product', 'name'],
      key: 'product',
    },
    {
      title: 'Date Réception',
      dataIndex: ['reception', 'reception_date'],
      key: 'reception_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Origine',
      dataIndex: ['reception', 'origin'],
      key: 'origin',
      render: (text) => text || '-',
    },
    {
      title: 'Quantité Initiale',
      dataIndex: ['reception', 'total_weight_kg'],
      key: 'initial_weight',
      align: 'right',
      render: (value) => `${parseFloat(value).toFixed(3)} kg`,
    },
    {
      title: 'Stock Actuel',
      dataIndex: 'current_stock_kg',
      key: 'current_stock',
      align: 'right',
      render: (value) => `${parseFloat(value || 0).toFixed(3)} kg`,
    },
    {
      title: 'Commandes',
      dataIndex: 'orders',
      key: 'orders_count',
      align: 'center',
      render: (orders) => (
        <Badge count={orders?.length || 0} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: 'Clients',
      key: 'customers',
      render: (_, record) => {
        const customers = [...new Set(record.orders?.map(o => o.customer?.name) || [])];
        return customers.length > 0 ? (
          <Tooltip title={customers.join(', ')}>
            <Text>{customers.length} client(s)</Text>
          </Tooltip>
        ) : '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Voir traçabilité">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Rapport PDF">
            <Button
              type="text"
              icon={<FilePdfOutlined />}
              onClick={() => handleGenerateReport({ lot_number: record.reception?.lot_number })}
              loading={generateReportMutation.isLoading}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const statsData = stats?.data?.data || {};

  return (
    <div>
      <Title level={2}>
        <SearchOutlined /> Système de Traçabilité
      </Title>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Lots Tracés"
              value={statsData.total_lots || 0}
              prefix={<NodeIndexOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Poids Total Tracé"
              value={statsData.total_weight || 0}
              suffix="kg"
              precision={3}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Clients Servis"
              value={statsData.unique_customers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="stats-card">
            <Statistic
              title="Livraisons Effectuées"
              value={statsData.total_deliveries || 0}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Search Interface */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={4}>
            <Select
              value={searchType}
              onChange={setSearchType}
              style={{ width: '100%' }}
            >
              <Option value="lot_number">N° Lot</Option>
              <Option value="customer_name">Client</Option>
              <Option value="order_number">N° Commande</Option>
              <Option value="delivery_number">N° Livraison</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Input
              placeholder={`Rechercher par ${searchType === 'lot_number' ? 'numéro de lot' :
                searchType === 'customer_name' ? 'nom du client' :
                searchType === 'order_number' ? 'numéro de commande' : 'numéro de livraison'}...`}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col xs={24} sm={5}>
            <Select
              placeholder="Filtrer par produit"
              allowClear
              value={selectedProduct}
              onChange={setSelectedProduct}
              style={{ width: '100%' }}
            >
              {products?.data?.data?.map((product) => (
                <Option key={product.id} value={product.id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={5}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Date début', 'Date fin']}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={isLoading}
              >
                Rechercher
              </Button>
              <Button
                icon={<BarChartOutlined />}
                onClick={() => setReportModalVisible(true)}
              >
                Rapport
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Results */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <BranchesOutlined />
                Résultats de Traçabilité
                {traceabilityData?.data?.data?.length && (
                  <Badge count={traceabilityData.data.data.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            }
            key="search"
          >
            {traceabilityData?.data?.data?.length > 0 ? (
              <Table
                columns={columns}
                dataSource={traceabilityData.data.data}
                loading={isLoading}
                rowKey={(record) => record.reception?.id || record.id}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `${range[0]}-${range[1]} sur ${total} résultats`,
                }}
                scroll={{ x: 1200 }}
              />
            ) : traceabilityData ? (
              <Empty
                description="Aucun résultat trouvé"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <AuditOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">
                    Utilisez les filtres ci-dessus pour rechercher dans la traçabilité
                  </Text>
                </div>
              </div>
            )}
          </TabPane>

          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                Analyse
              </span>
            }
            key="analysis"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="Répartition par Produit" size="small">
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Text type="secondary">Graphique en développement</Text>
                  </div>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="Évolution Mensuelle" size="small">
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Text type="secondary">Graphique en développement</Text>
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* Traceability Details Modal */}
      <TraceabilityDetailsModal
        trace={selectedTrace}
        visible={traceDetailsVisible}
        onClose={() => {
          setTraceDetailsVisible(false);
          setSelectedTrace(null);
        }}
      />

      {/* Report Generation Modal */}
      <Modal
        title="Générer un Rapport de Traçabilité"
        open={reportModalVisible}
        onCancel={() => {
          setReportModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGenerateReport}
        >
          <Form.Item
            name="report_type"
            label="Type de Rapport"
            rules={[{ required: true, message: 'Sélectionnez le type de rapport' }]}
          >
            <Select placeholder="Type de rapport">
              <Option value="lot_summary">Résumé par Lot</Option>
              <Option value="customer_summary">Résumé par Client</Option>
              <Option value="product_summary">Résumé par Produit</Option>
              <Option value="full_traceability">Traçabilité Complète</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="date_range"
            label="Période"
            rules={[{ required: true, message: 'Sélectionnez la période' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="product_id"
            label="Produit (optionnel)"
          >
            <Select placeholder="Filtrer par produit" allowClear>
              {products?.data?.data?.map((product) => (
                <Option key={product.id} value={product.id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setReportModalVisible(false)}>
                Annuler
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={generateReportMutation.isLoading}
                icon={<FilePdfOutlined />}
              >
                Générer le Rapport
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

// Traceability Details Modal Component
const TraceabilityDetailsModal = ({ trace, visible, onClose }) => {
  if (!trace) return null;

  const getTraceabilitySteps = (trace) => {
    const steps = [];

    // Reception
    if (trace.reception) {
      steps.push({
        title: 'Réception',
        description: `Lot ${trace.reception.lot_number}`,
        status: 'finish',
        icon: <InboxOutlined />,
        date: trace.reception.reception_date,
        details: {
          quantity: `${trace.reception.quantity_heads} têtes`,
          weight: `${trace.reception.total_weight_kg} kg`,
          origin: trace.reception.origin,
          slaughter_date: trace.reception.slaughter_date,
        },
      });
    }

    // Stock
    if (trace.stock_movements?.length > 0) {
      steps.push({
        title: 'Gestion Stock',
        description: `${trace.stock_movements.length} mouvement(s)`,
        status: 'finish',
        icon: <DatabaseOutlined />,
        date: trace.stock_movements[0]?.created_at,
        details: {
          current_stock: `${trace.current_stock_kg || 0} kg`,
          movements: trace.stock_movements.length,
          last_movement: trace.stock_movements[0]?.movement_type,
        },
      });
    }

    // Orders
    if (trace.orders?.length > 0) {
      steps.push({
        title: 'Commandes',
        description: `${trace.orders.length} commande(s)`,
        status: 'finish',
        icon: <ShoppingCartOutlined />,
        date: trace.orders[0]?.order_date,
        details: {
          customers: [...new Set(trace.orders.map(o => o.customer?.name))].join(', '),
          total_amount: trace.orders.reduce((sum, o) => sum + o.total_amount, 0),
          total_weight: trace.orders.reduce((sum, o) =>
            sum + o.order_items?.reduce((itemSum, item) => itemSum + item.quantity_kg, 0), 0
          ),
        },
      });
    }

    // Deliveries
    if (trace.deliveries?.length > 0) {
      const allDelivered = trace.deliveries.every(d => d.status === 'delivered');
      steps.push({
        title: 'Livraisons',
        description: `${trace.deliveries.length} livraison(s)`,
        status: allDelivered ? 'finish' : 'process',
        icon: <TruckOutlined />,
        date: trace.deliveries[0]?.delivered_at || trace.deliveries[0]?.created_at,
        details: {
          delivered: trace.deliveries.filter(d => d.status === 'delivered').length,
          total: trace.deliveries.length,
          customers: [...new Set(trace.deliveries.map(d => d.order?.customer?.name))].join(', '),
        },
      });
    }

    return steps;
  };

  const steps = getTraceabilitySteps(trace);

  return (
    <Modal
      title={`Traçabilité - Lot ${trace.reception?.lot_number}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
    >
      <Tabs defaultActiveKey="timeline">
        <TabPane tab="Chronologie" key="timeline">
          <Steps
            direction="vertical"
            current={steps.length - 1}
            items={steps.map((step, index) => ({
              title: step.title,
              description: (
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    <Text>{step.description}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      <CalendarOutlined /> {dayjs(step.date).format('DD/MM/YYYY HH:mm')}
                    </Text>
                  </div>
                  <Card size="small" style={{ marginTop: '8px' }}>
                    <Descriptions column={2} size="small">
                      {Object.entries(step.details).map(([key, value]) => (
                        <Descriptions.Item
                          key={key}
                          label={key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        >
                          {typeof value === 'number' ? value.toLocaleString() : value}
                        </Descriptions.Item>
                      ))}
                    </Descriptions>
                  </Card>
                </div>
              ),
              status: step.status,
              icon: step.icon,
            }))}
          />
        </TabPane>

        <TabPane tab="Détails Réception" key="reception">
          {trace.reception && (
            <Card>
              <Descriptions title="Informations de Réception" column={2}>
                <Descriptions.Item label="N° Lot">
                  <Tag color="blue">{trace.reception.lot_number}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Produit">
                  {trace.reception.product?.name}
                </Descriptions.Item>
                <Descriptions.Item label="Type Animal">
                  {trace.reception.product?.animal_type}
                </Descriptions.Item>
                <Descriptions.Item label="Origine">
                  {trace.reception.origin || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Quantité">
                  {trace.reception.quantity_heads} têtes
                </Descriptions.Item>
                <Descriptions.Item label="Poids Total">
                  {trace.reception.total_weight_kg} kg
                </Descriptions.Item>
                <Descriptions.Item label="Poids Moyen">
                  {trace.reception.average_weight_kg} kg/tête
                </Descriptions.Item>
                <Descriptions.Item label="Date Abattage">
                  {dayjs(trace.reception.slaughter_date).format('DD/MM/YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Date Réception">
                  {dayjs(trace.reception.reception_date).format('DD/MM/YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Reçu par">
                  {trace.reception.user?.name}
                </Descriptions.Item>
              </Descriptions>

              {trace.reception.notes && (
                <div style={{ marginTop: '16px' }}>
                  <Text strong>Notes:</Text>
                  <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                    <Text>{trace.reception.notes}</Text>
                  </div>
                </div>
              )}
            </Card>
          )}
        </TabPane>

        <TabPane tab="Mouvements Stock" key="stock">
          <Table
            dataSource={trace.stock_movements || []}
            columns={[
              {
                title: 'Date',
                dataIndex: 'created_at',
                key: 'created_at',
                render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm'),
              },
              {
                title: 'Type',
                dataIndex: 'movement_type',
                key: 'movement_type',
                render: (type) => {
                  const types = {
                    in: { color: 'green', text: 'Entrée' },
                    out: { color: 'red', text: 'Sortie' },
                    adjustment: { color: 'orange', text: 'Ajustement' },
                  };
                  const config = types[type] || { color: 'default', text: type };
                  return <Tag color={config.color}>{config.text}</Tag>;
                },
              },
              {
                title: 'Quantité',
                dataIndex: 'quantity_kg',
                key: 'quantity_kg',
                align: 'right',
                render: (value, record) => {
                  const sign = record.movement_type === 'out' ? '-' : '+';
                  const color = record.movement_type === 'out' ? '#ff4d4f' : '#52c41a';
                  return (
                    <Text style={{ color }}>
                      {sign}{parseFloat(value).toFixed(3)} kg
                    </Text>
                  );
                },
              },
              {
                title: 'Référence',
                dataIndex: 'reference',
                key: 'reference',
                render: (text) => text || '-',
              },
              {
                title: 'Notes',
                dataIndex: 'notes',
                key: 'notes',
                render: (text) => text || '-',
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>

        <TabPane tab="Commandes" key="orders">
          <Table
            dataSource={trace.orders || []}
            columns={[
              {
                title: 'N° Commande',
                dataIndex: 'order_number',
                key: 'order_number',
                render: (text) => <Tag color="blue">{text}</Tag>,
              },
              {
                title: 'Client',
                dataIndex: ['customer', 'name'],
                key: 'customer',
              },
              {
                title: 'Date',
                dataIndex: 'order_date',
                key: 'order_date',
                render: (date) => dayjs(date).format('DD/MM/YYYY'),
              },
              {
                title: 'Quantité',
                key: 'quantity',
                align: 'right',
                render: (_, record) => {
                  const totalKg = record.order_items?.reduce((sum, item) => sum + item.quantity_kg, 0) || 0;
                  return `${totalKg.toFixed(3)} kg`;
                },
              },
              {
                title: 'Montant',
                dataIndex: 'total_amount',
                key: 'total_amount',
                align: 'right',
                render: (value) => `${parseFloat(value).toFixed(2)} DA`,
              },
              {
                title: 'Statut',
                dataIndex: 'status',
                key: 'status',
                render: (status) => {
                  const configs = {
                    pending: { color: 'orange', text: 'En attente' },
                    confirmed: { color: 'blue', text: 'Confirmée' },
                    delivered: { color: 'green', text: 'Livrée' },
                    cancelled: { color: 'red', text: 'Annulée' },
                  };
                  const config = configs[status] || { color: 'default', text: status };
                  return <Tag color={config.color}>{config.text}</Tag>;
                },
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>

        <TabPane tab="Livraisons" key="deliveries">
          <Table
            dataSource={trace.deliveries || []}
            columns={[
              {
                title: 'N° Livraison',
                dataIndex: 'delivery_number',
                key: 'delivery_number',
                render: (text) => <Tag color="purple">{text}</Tag>,
              },
              {
                title: 'Client',
                dataIndex: ['order', 'customer', 'name'],
                key: 'customer',
              },
              {
                title: 'Adresse',
                dataIndex: 'delivery_address',
                key: 'delivery_address',
                render: (text) => (
                  <Tooltip title={text}>
                    <Text ellipsis style={{ maxWidth: '200px' }}>
                      <EnvironmentOutlined /> {text}
                    </Text>
                  </Tooltip>
                ),
              },
              {
                title: 'Date Livraison',
                dataIndex: 'delivered_at',
                key: 'delivered_at',
                render: (date) => date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
              },
              {
                title: 'Livreur',
                dataIndex: ['delivery_person', 'name'],
                key: 'delivery_person',
                render: (text) => text || '-',
              },
              {
                title: 'Statut',
                dataIndex: 'status',
                key: 'status',
                render: (status) => {
                  const configs = {
                    pending: { color: 'orange', text: 'En attente' },
                    in_transit: { color: 'blue', text: 'En transit' },
                    delivered: { color: 'green', text: 'Livrée' },
                    returned: { color: 'red', text: 'Retournée' },
                  };
                  const config = configs[status] || { color: 'default', text: status };
                  return <Tag color={config.color}>{config.text}</Tag>;
                },
              },
            ]}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default Traceability;
